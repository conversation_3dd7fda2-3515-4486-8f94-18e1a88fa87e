import axios from "axios";
import { WbPlatform } from "../entities/platform";
import { Spilder, SpilderError } from "./spilder";

export class <PERSON>utiaoSpilder extends Spilder {
    getRealtimeArticleLinks(keyword: string): Promise<{ links: string[]; keyword: string; count: number; }> {
        throw new SpilderError(-200,"暂不支持头条");
    }
    loadCommentCount(id: number, uid: number): Promise<number> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    loadChildCommentCount(id: number, uid: number): Promise<number> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicArticleLongTextDetail(id: string): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createAccount(uid: number): Promise<any> {
        throw new SpilderError(-200, `暂不支持知乎`)
    }
    loadChildComments(id: number, uid: number): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    loadMoreChildComments(id: number, uid: number, max_id: number, total: number): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    loadMoreComments(id: number, uid: number, max_id: number, total: number): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    loadComments(id: number, uid: number): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicLinks(link: string): Promise<{ list: string; detail: string; }[]> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicDetail(link: string): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicArticlePageLinks(link: string): Promise<string[]> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicArticleLinks(page: string): Promise<string[]> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicArticleDetail(link: string): Promise<any> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    createTopicPageLinks(keyword: string): Promise<{ links: string[]; keyword: string; count: number; }> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    loginSuccess(): Promise<WbPlatform | undefined> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    checkScanStatus(): Promise<string | undefined> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
    async setCookies(cookies: any[]): Promise<void> {
        if (this.page) {
            await this.page.setCookie(...cookies);
        }
    }
    async isLogin(): Promise<boolean> {
        const cookies = this.platform.cookies;
        return axios.get(`https://www.zhihu.com/api/v4/me?include=visits_count%2Cdraft_count`, {
            headers: {
                cookies: this.cookies
            }
        }).then(res => res.data).then(data => {
            return !!data
        }).catch(e => {
            return false;
        })
    }
    createLoginQrCode(): Promise<string | undefined> {
        throw new SpilderError(-200, `暂不支持头条`)
    }
}