import { Controller, Get, Injector, defer, Inject } from "@nger/core";
import { Subject } from "rxjs";
import { Post, Body } from '@nger/http'
import { ZhihuLoginService } from "../services/zhihu.login";
import { CONTEXT } from "@nger/http";
import { URLSearchParams } from "url";
import { ToutiaoLoginService } from "../services/toutiao.login";
import { QqNewLoginService } from "../services/qq-new.login";
import { Db } from '@nger/typeorm'
import { SpiderUser } from "../entities/spider_user";
import { PUPPETEER_POOL } from "../tokens";
import { Browser, Page, Target } from "puppeteer";
import { delay, get, lock, unLock, waitFor, waitUnLock } from '../services/redis'

@Controller('@nger/weibo/bind-user')
export class BindUserController {
    get zhihu() {
        return this.injector.get(ZhihuLoginService)
    }
    get to<PERSON>ao() {
        return this.injector.get(ToutiaoLoginService)
    }
    get qqNew() {
        return this.injector.get(QqNewLoginService)
    }
    get db() {
        return this.injector.get(Db)
    }
    constructor(private injector: Injector) { }
    @Post('update_or_add_user')
    async updateOrAddUser(@Body() user: any) {
        const r = this.db.getRepository(SpiderUser)
        const item = await r.findOneBy({ username: user.username, platform: user.platform })
        if (item) {
            await r.update(item.id, {
                cookies: user.cookies,
                status: user.status,
            })
        } else {
            await r.insert({
                cookies: user.cookies,
                status: user.status,
                platform: user.platform,
                username: user.username
            })
        }
        return user;
    }

    @Get('login_qrcode')
    async login_qrcode(@Inject(CONTEXT) ctx: any) {
        const login_state = new Subject()
        const req = ctx.request;
        const query = req.query as URLSearchParams
        const platform = query.get('platform')
        const reqid = Date.now();
        switch (platform) {
            case 'weibo':
                this.open_weibo(`${reqid}`, login_state)
                break;
            case 'zhihu':
                this.zhihu.login(login_state)
                break;
            case 'toutiao':
                this.toutiao.login(login_state)
                break;
            case 'qq-new':
                this.qqNew.login(login_state)
                break;
            case 'wechat':
                break;
            case 'douyin':
                break;
            case 'baidu':
                break;
        }
        return login_state.asObservable()
    }
    private delay(time: number) {
        const d = defer();
        setTimeout(() => {
            d.resolve();
        }, time)
        return d;
    }
    get puppeteerPool() {
        return this.injector.get(PUPPETEER_POOL)
    }
    private async open_weibo(reqId: string, login_state: Subject<any>) {
        let page: Page | null = null;
        let browser: Browser | null = null;
        try {
            browser = await this.puppeteerPool.acquire()
            const onResponse = async (r: any) => {
                const url = r.url()
                if (url.startsWith(`https://v2.qr.weibo.cn/inf/gen`)) {
                    const image = await r.buffer()
                    await lock(reqId, image.toString('base64'))
                    login_state.next({
                        action: 'get_login_qrcode',
                        data: `data:image/png;base64,${image.toString('base64')}`
                    });
                    return;
                }
                if (url.startsWith('https://weibo.com/ajax/side/cards/sideInterested')) {
                    const data = await r.json()
                    if (data.ok === 1) {
                        const uid = data.data.uid;
                        await unLock(reqId)
                        await lock(`uid.${reqId}`, uid)
                    }
                }
                if (url.startsWith(`https://weibo.com/ajax/profile/info`)) {
                    const info = await r.json()
                    if(info.ok === 1){
                        await lock(`userInfo.${reqId}`, JSON.stringify(info.data.user))
                    }
                }
            }
            browser.on('*', async (...args: any[]) => {
                if (args && args.length > 0) {
                    const [type, payload] = args;
                    if (type === 'targetcreated') {
                        const target: Target = payload;
                        const type = target.type()
                        if (type === 'page') {
                            const page: any = await (target).page()
                            page && page.on('*', (...args: any[]) => {
                                if (args && args.length > 0) {
                                    const [type, payload] = args;
                                    if (type === 'response') {
                                        onResponse(payload)
                                    }
                                }
                            })
                        }
                    }
                }
            })
            const url = `https://s.weibo.com`;
            const ctx = browser.defaultBrowserContext();
            await ctx.overridePermissions(url, ['geolocation'])
            page = await browser.newPage();
            (page as any).on('*', (...args: any[]) => {
                if (args && args.length > 0) {
                    const [type, payload] = args;
                    if (type === 'response') {
                        onResponse(payload)
                    }
                }
            });
            await page.setJavaScriptEnabled(true);
            await page.setViewport({
                width: 1920,
                height: 768
            });
            await page.goto(url, {
                waitUntil: ['networkidle2']
            });
            await this.delay(20)
            const href1 = page.url();
            login_state.next({
                action: 'open',
                data: href1
            });
            const loginBtn = await page.waitForSelector('div.woo-box-flex.woo-tab-nav > a:nth-child(5)').catch(e => {
                return null;
            });
            if (!loginBtn) {
                login_state.next({
                    action: 'get_login_fail',
                    data: href1
                });
                login_state.complete();
                return;
            }
            const href = page.url();
            login_state.next({
                action: 'get_login_btn',
                data: href
            });
            await loginBtn.click();
            login_state.next({
                action: 'click_login_btn',
                data: href
            })
            await waitFor(reqId);
            console.log(`登陆二维码获取成功`)
            login_state.next({
                action: 'get_scan_qrcode',
                data: href
            })
            // 等待扫码登陆
            const uid = await waitFor(`uid.${reqId}`)
            console.log(`确认扫码成功${uid}`)
            await delay(1000)
            const cookies = await page.cookies()
            login_state.next({
                action: 'login_success',
                data: url,
                cookies
            });
            await page.goto(`https://weibo.com/u/${uid}`, { waitUntil: 'networkidle2' })
            // 获取用户信息
            const userInfo = await waitFor(`userInfo.${reqId}`)
            await unLock(`userInfo.${reqId}`)
            login_state.next({
                action: 'get_userinfo',
                data: {
                    uid,
                    cookies,
                    name: userInfo.screen_name,
                    avatar: userInfo.avatar_hd,
                    gender: userInfo.gender
                }
            })
        } catch (e: any) {
            login_state.next({
                action: 'get_login_fail',
                data: e.message
            })
            login_state.error(e)
        } finally {
            login_state.complete();
            if (page) {
                await page.close();
            }
            if (browser) {
                await this.puppeteerPool.destroy(browser)
            }
        }
    }
}
