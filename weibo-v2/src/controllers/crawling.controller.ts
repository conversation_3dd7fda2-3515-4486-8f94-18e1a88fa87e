import { Controller, Get, Injector } from "@nger/core";
import { Error400, UserLogoutError } from "../framework/tasks/error";
import { PUPPETEER_POOL, WEIBO_USER_POOL } from "../tokens";
import { load } from 'cheerio'
import { Body, Post, Query } from "@nger/http";
import axios from "axios";
import { <PERSON>rows<PERSON> } from "puppeteer";

@Controller('@nger/weibo/crawling')
export class CrawlingController {
    get weiboUserPool() {
        return this.injector.get(WEIBO_USER_POOL)
    }
    get pupperPool() {
        return this.injector.get(PUPPETEER_POOL)
    }
    constructor(private injector: Injector) { }

    @Post('weiboNlp')
    weiboNlp(@Query('url') url: string, @Body('text') text: string[]) { }

    @Get('weiboShare')
    weiboShare(@Query('url') url: string) {
        return this._weiboShare(url).catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }

    private async _weiboShare(url: string) {
        const headers = await this.getHeaders();
        return axios.get(url, {
            headers: headers
        }).then(res => res.data)
    }

    @Get('weiboLike')
    weiboLike(@Query('url') url: string) {
        return this._weiboLike(url).catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }
    private async _weiboLike(url: string) {
        const headers = await this.getHeaders();
        return axios.get(url, {
            headers: headers
        }).then(res => res.data)
    }

    @Get('weiboComment')
    async weiboComment(@Query('url') url: string) {
        return this._weiboComment(url).catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }

    private async _weiboComment(url: string) {
        const headers = await this.getHeaders();
        return axios.get(url, {
            headers: headers
        }).then(r => r.data)
    }

    @Get('weiboChildComment')
    async weiboChildComment(@Query('url') url: string) {
        return this._weiboChildComment(url).catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }

    private async _weiboChildComment(url: string) {
        const headers = await this.getHeaders();
        return axios.get(url, {
            headers: headers
        }).then(r => r.data)
    }

    private async getHeaders() {
        const user = await this.weiboUserPool.acquire()
        const cookies: any[] = user.cookies;
        const headers = {
            cookie: cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
            ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
        };
        this.weiboUserPool.release(user)
        return headers;
    }

    @Post('saveArticle')
    saveArticle(@Body() article: any) {
        return { article }
    }
    @Get('weiboDetail')
    async ajaxDetail(@Query('url') url: string) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            const cookies = user.cookies;
            const headers = {
                cookie: cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; '),
                ['user-agent']: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36'
            };
            return axios.get(url, { headers }).then(res => {
                return { success: true, data: res.data }
            }).catch(e => {
                return {
                    success: false,
                    message: e.message
                }
            });
        }
        return {
            success: false,
            message: 'user logout'
        }
    }
    @Get('getUser')
    async getUser() {
        return this._getUser().catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }
    private async _getUser() {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user)
            return user;
        }
        throw new Error(`没有可用登陆用户`)
    }
    @Get('weiboListUrl')
    async weiboListUrl(@Query('url') url: string) {
        return this._weiboListUrl(url).catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }
    private async _getBrowser(url: string) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            const browser = await this.pupperPool.acquire();
            if (browser) {
                this.pupperPool.release(browser)
                return browser;
            }
        }
    }
    private async _weiboListUrl(url: string) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            const browser = await this.pupperPool.acquire();
            if (browser) {
                this.pupperPool.release(browser)
                const defaultCtx = browser.defaultBrowserContext();
                await defaultCtx.overridePermissions(url, ['geolocation'])
                const page = await browser.newPage();
                await page.setJavaScriptEnabled(true);
                const cookies = user.cookies;
                await page.setCookie(...cookies);
                this.currentUser = user;
                await page.setViewport({
                    width: 1920,
                    height: 768
                });
                await page.goto(url, {
                    waitUntil: ['networkidle2']
                });
                await this.delay(300);
                const href = page.url();
                if (href.startsWith('https://passport')) {
                    await this.weiboUserPool.destroy(user)
                    throw new UserLogoutError()
                }
                if (href.startsWith('search_need_login')) {
                    await this.weiboUserPool.destroy(user)
                    this.currentUser = undefined;
                    throw new UserLogoutError()
                }
                const content = await page.content()
                if (content.includes('404 Page not found')) {
                    throw new Error400();
                }
                if (content.includes('抱歉，未找到')) {
                    throw new Error400();
                }
                const $ = load(content);
                const element = $('.loginBtn');
                if (element.length > 0) {
                    await this.weiboUserPool.destroy(user);
                    this.currentUser = undefined;
                    throw new UserLogoutError();
                }
                let listUrls: string[] = [];
                const moreList = $('ul[node-type="feed_list_page_morelist"] > li')
                moreList.map((index, el) => {
                    const cur = el.attribs['class'] && el.attribs['class'].includes('cur')
                    if (!cur) {
                        const $ = load(el)
                        const a = $('a')[0]
                        const href = a.attribs['href'];
                        const url = this.parseUrl(`https://s.weibo.com${href}`)!
                        listUrls.push(url)
                    }
                });
                await page.close();
                await this.pupperPool.destroy(browser)
                return listUrls
            }
        }
        return [];
    }
    @Get('weiboDetailUrl')
    async weiboDetailUrl(@Query('url') url: string) {
        return this._weiboDetailUrl(url).catch(e => {
            return {
                success: false,
                message: e.message
            }
        })
    }
    currentUser: any;
    private async _weiboDetailUrl(url: string) {
        const user = await this.weiboUserPool.acquire();
        if (user) {
            this.weiboUserPool.release(user);
            const browser = await this.pupperPool.acquire();
            if (browser) {
                this.pupperPool.release(browser)
                const defaultCtx = browser.defaultBrowserContext();
                await defaultCtx.overridePermissions(url, ['geolocation'])
                const page = await browser.newPage();
                await page.setJavaScriptEnabled(true);
                const cookies = user.cookies;
                await page.setCookie(...cookies);
                await page.setViewport({
                    width: 1920,
                    height: 768
                });
                await page.goto(url, {
                    waitUntil: ['networkidle2']
                });
                await this.delay(300);
                const href = page.url();
                if (href.startsWith('https://passport')) {
                    await this.weiboUserPool.destroy(user)
                    throw new UserLogoutError()
                }
                if (href.startsWith('search_need_login')) {
                    await this.weiboUserPool.destroy(user)
                    throw new UserLogoutError()
                }
                const content = await page.content()
                if (content.includes('404 Page not found')) {
                    throw new Error400();
                }
                if (content.includes('抱歉，未找到')) {
                    throw new Error400();
                }
                const $ = load(content);
                const element = $('.loginBtn');
                if (element.length > 0) {
                    await this.weiboUserPool.destroy(user);
                    this.currentUser = undefined;
                    throw new UserLogoutError();
                }
                // article url
                const links = $('#pl_feedlist_index .card-feed')
                console.log(links.length)
                let urls: string[] = [];
                links.map((index, item) => {
                    try {
                        const $ = load(item);
                        const linkElement = $('div.content > .from > a');
                        const link = linkElement[0]?.attribs['href'];
                        const url = this.parseUrl(link);
                        url && urls.push(url)
                    } catch (e) {
                        // console.error(e)
                    }
                });
                await page.close();
                await this.pupperPool.destroy(browser)
                return urls;
            }
        }
        return [];
    }
    private parseUrl(url: string) {
        if (!url) {
            return;
        }
        if (url.startsWith('https')) {
            return url
        } else {
            return `https:${url}`
        }
    }
    private delay(n: number) {
        return new Promise<void>((resolve, reject) => {
            setTimeout(() => {
                resolve()
            }, n)
        })
    }
}
