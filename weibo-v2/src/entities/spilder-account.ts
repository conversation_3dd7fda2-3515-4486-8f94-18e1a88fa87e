import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, Unique, UpdateDateColumn } from "typeorm";
import { SpilderTopic } from "./spilder-topic";
import { SpilderTopicArticle } from "./spilder-topic-article";
import { SpilderTopicArticleComment } from "./spilder-topic-article-comment";

@Component({
    title: '媒体'
})
@Entity({
    name: 'spilder_account'
})
@Unique(['uid','platform'])
export class SpilderAccount {
    @Widget({
        title: '序号',
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'number'
        }
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: 'ID',
        hideInSearch: true
    })
    @Column()
    uid!: string;

    @Widget({
        title: '账号'
    })
    @Column()
    nickname!: string;

    @Widget({
        title: '平台',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    })
    @Column({
        default: 'weibo'
    })
    platform!: string;

    @Widget({
        title: '账号类型'
    })
    @Column({
        default: '自媒体'
    })
    type!: string;

    @Widget({
        title: '是否认证',
        hideInSearch: true,
        widget: {
            type: 'boolean'
        }
    })
    @Column({
        default: ''
    })
    verified!: string;

    @Widget({
        title: '认证类型',
        sortable: true,
        width: '160px',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '未认证',
                    value: -1
                }, {
                    label: '微博个人认证',
                    value: 2
                }, {
                    label: '微博官方认证',
                    value: 1
                }]
            }
        }
    })
    @Column({
        default: 0,
    })
    verified_type!: number;

    @Widget({
        title: '认证信息',
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    verified_reason!: string;

    @Widget({
        title: '认证类型标题',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    verified_type_ext!: string;

    @Widget({
        title: '微号',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    weihao!: string;

    @Widget({
        title: '所属媒体',
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    from!: string;

    @Widget({
        title: '连接',
        hideInSearch: true,
        widget: {
            type: 'link',
            label: 'nickname'
        }
    })
    @Column({
        default: ''
    })
    url!: string;

    @Widget({
        title: '地区',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    location!: string;

    @Widget({
        title: '关注数',
        hideInSearch: true,
        hideInTable: true,
        sortable: true
    })
    @Column({
        default: 0,
        type: 'bigint'
    })
    followers_count!: number;

    @Widget({
        title: '朋友数',
        hideInSearch: true,
        hideInTable: true,
        sortable: true
    })
    @Column({
        default: 0,
        type: 'bigint'
    })
    friends_count!: number;

    @Widget({
        title: '详情',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    description!: string;

    @Widget({
        title: '创建时间',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    created_at!: string;

    @Widget({
        title: '性别',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    gender!: string;

    @Widget({
        title: '粉丝数',
        hideInSearch: true,
        hideInTable: true,
        sortable: true
    })
    @Column({
        default: 0
    })
    statuses_count!: number;

    @Widget({
        title: '个人连接',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    profile_url!: string;

    @Widget({
        title: '头像',
        width: '65px',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'image',
            config: {
                style: {
                    width: '65px',
                }
            }
        }
    })
    @Column({
        default: ''
    })
    profile_image_url!: string;

    @Widget({
        title: '公司',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    company!: string;

    @Widget({
        title: '学校',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    education!: string;

    @Widget({
        title: '行业',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    career!: string;

    @Widget({
        title: '生日',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    birthday!: string;

    @Widget({
        title: '信誉',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    sunshine_credit!: string;

    @Widget({
        title: '标签',
        hideInTable: true,
        hideInSearch: true
    })
    @Column('simple-array')
    tags!: string[];

    @Widget({
        title: '认证小类',
        sortable: true,
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'number'
        }
    })
    @Column({
        default: 0
    })
    mbrank!: number;

    @Widget({
        title: '认证大类',
        sortable: true,
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: 0
    })
    mbtype!: number;

    @Widget({
        hideInTable: true,
        hideInSearch: true,
        sortable: true,
        widget: {
            type: 'number'
        }
    })
    @Column({
        default: 0
    })
    pc_new!: number;

    @Widget({
        hideInTable: true,
        hideInSearch: true,
        sortable: true,
        widget: {
            type: 'number'
        }
    })
    @Column({
        default: 0
    })
    top_user!: number;

    @Widget({
        title: 'V',
        sortable: true,
        widget: {
            type: 'radio',
            config: {
                options: [{
                    label: '无',
                    value: 0
                }, {
                    label: '黄',
                    value: 1
                }, {
                    label: '灰',
                    value: 2
                }, {
                    label: '蓝',
                    value: 3
                }, {
                    label: '红',
                    value: 4
                }]
            }
        }
    })
    @Column({
        default: 0
    })
    user_type!: number;

    @Widget({
        title: '问答',
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    wenda!: string;

    @Widget({
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: false
    })
    planet_video!: boolean;

    @Widget({
        hideInTable: true,
        hideInSearch: true
    })
    @Column({
        default: false,
    })
    is_muteuser!: boolean;

    @Widget({
        title: '话题',
        widget: {
            type: 'array',
            from: 'spilder_topic',
            where: {
                uid: 'uid'
            }
        }
    })
    @OneToMany(() => SpilderTopic, it => it.uid, {
        createForeignKeyConstraints: false
    })
    topics!: SpilderTopic[];


    @Widget({
        title: '帖子',
        widget: {
            type: 'array',
            from: 'spilder_topic_article',
            where: {
                uid: 'uid'
            }
        }
    })
    @OneToMany(() => SpilderTopicArticle, it => it.uid, {
        createForeignKeyConstraints: false
    })
    articles!: SpilderTopicArticle[];

    @Widget({
        title: '评论',
        widget: {
            type: 'array',
            from: 'spilder_topic_article_comment',
            where: {
                uid: 'uid'
            }
        }
    })
    @OneToMany(() => SpilderTopicArticleComment, it => it.uid, {
        createForeignKeyConstraints: false
    })
    comments!: SpilderTopicArticleComment[];

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;

    @Widget({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @UpdateDateColumn({
        name: 'update_date'
    })
    updateDate!: Date;

}