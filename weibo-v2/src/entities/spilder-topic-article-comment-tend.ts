import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Component({
    title: '评论截面'
})
@Entity({
    name: 'spilder_topic_article_comment_tend'
})
export class SpilderTopicArticleCommentTend {

    @Widget({
        title: '序号'
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '评论'
    })
    @Column()
    cid!: number;

    @Widget({
        title: '点赞',
        hideInSearch: true
    })
    @Column({
        nullable: true,
        type: 'bigint'
    })
    like_counts!: number;

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;
}
