import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { WbEvent } from "./event";
import { WbPlatform } from "./platform";
import { SpilderAccount } from "./spilder-account";
import { SpilderTopic } from "./spilder-topic";
import { SpilderTopicArticleComment } from "./spilder-topic-article-comment";

@Component({
    title: '帖子'
})
@Entity({
    name: 'spilder_topic_article'
})
export class SpilderTopicArticle {

    @Widget({
        title: '序号',
        hideInSearch: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '话题',
        path: 'topic.title',
        widget: {
            type: 'selector',
            from: 'spilder_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    })
    @Column()
    tid!: number;

    @Widget({
        title: '话题'
    })
    @ManyToOne(() => SpilderTopic, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'tid',
        referencedColumnName: 'id'
    })
    topic!: SpilderTopic;

    @Widget({
        title: '事件',
        path: 'event.title',
        widget: {
            type: 'selector',
            from: 'wb_event',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    })
    @Column()
    eid!: number;

    @Widget({
        title: '事件'
    })
    @ManyToOne(() => WbEvent, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'eid',
        referencedColumnName: 'id'
    })
    event!: WbEvent;

    @Widget({
        title: '平台',
        path: 'platformEntity.title',
        widget: {
            type: 'selector',
            from: 'wb_platform',
            config: {
                label: 'title',
                value: 'name'
            }
        }
    })
    @Column({
        default: 'weibo'
    })
    platform!: string;

    @Widget({
        title: '平台'
    })
    @ManyToOne(() => WbPlatform, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'platform',
        referencedColumnName: 'name'
    })
    platformEntity!: WbPlatform;

    @Widget({
        title: '发帖时间',
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    })
    @Column({
        nullable: true
    })
    create_at!: Date;

    @Widget({
        title: '文章id',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    mid!: string;

    @Widget({
        title: '文章id',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    mblogid!: string;

    @Widget({
        title: '标题标签',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    title!: string;

    @Widget({
        title: '用户ID',
        hideInSearch: true,
        hideInTable: true
    })
    @Column({
        default: ''
    })
    uid!: string;

    @Widget({
        title: '媒体',
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    nickname!: string;

    @Widget({
        title: '媒体类型',
        path: 'account.type',
        hideInSearch: true
    })
    user_type!: string;

    @Widget({
        title: '媒体'
    })
    @ManyToOne(() => SpilderAccount, it => it.uid, {
        createForeignKeyConstraints: false
    })
    @JoinColumn({
        name: 'uid',
        referencedColumnName: 'uid'
    })
    account!: SpilderAccount;

    @Widget({
        title: '帖子文本',
        hideInSearch: true,
        width: '220px',
        widget: {
            type: 'textarea',
        }
    })
    @Column({
        type: 'text'
    })
    text_raw!: string;

    @Widget({
        title: '来源',
        hideInSearch: true
    })
    @Column({
        default: ''
    })
    source!: string;

    @Widget({
        title: '转发数',
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    })
    @Column({
        type: 'bigint',
        default: 0
    })
    reposts_count!: number;

    @Widget({
        title: '评论数',
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    })
    @Column({
        type: 'bigint',
        default: 0
    })
    comments_count!: number;

    @Widget({
        title: '点赞数',
        hideInSearch: true,
        widget: {
            type: 'number'
        }
    })
    @Column({
        type: 'bigint',
        default: 0
    })
    attitudes_count!: number;

    @Widget({
        title: '大图',
        hideInSearch: true,
        hideInTable: true,
        widget: {
            type: 'image',
            config: {
                style: {
                    width: '65px',
                }
            }
        }
    })
    @Column({
        default: '',
    })
    pic_bg_new!: string;

    @Widget({
        title: '关联话题',
        hideInSearch: true,
        hideInTable: true
    })
    @Column('simple-array', {
        nullable: true
    })
    structs!: string[];

    @Widget({
        title: '有无图片',
        hideInSearch: true,
        widget: {
            type: 'boolean',
            checked: '是',
            unchecked: '否'
        }
    })
    @Column({
        name: 'has_image',
        default: false
    })
    hasImage!: boolean;

    @Widget({
        title: '有无视频',
        hideInSearch: true,
        widget: {
            type: 'boolean',
            checked: '是',
            unchecked: '否'
        }
    })
    @Column({
        name: 'has_video',
        default: false
    })
    hasVideo!: boolean;

    @Widget({
        title: '有无连接',
        hideInSearch: true,
        widget: {
            type: 'boolean',
            checked: '是',
            unchecked: '否'
        }
    })
    @Column({
        name: 'has_link',
        default: false
    })
    hasLink!: boolean;

    @Widget({
        title: '地址',
        hideInSearch: true,
    })
    @Column({
        default: ''
    })
    region_name!: string;

    @Widget({
        title: '长度',
        hideInSearch: true,
    })
    @Column({
        default: 0,
        name: 'text_length'
    })
    textLength!: number;

    @Widget({
        title: '评论',
        widget: {
            type: 'array',
            where: { aid: 'id' }
        }
    })
    @OneToMany(() => SpilderTopicArticleComment, it => it.aid, {
        createForeignKeyConstraints: false
    })
    comments!: SpilderTopicArticleComment[];

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;

    @Widget({
        title: '更新时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true,
        widget: {
            type: 'date',
            config: {
                showTime: true
            }
        }
    })
    @UpdateDateColumn({
        name: 'update_date'
    })
    updateDate!: Date;

    // @Column({
    //     type: 'text',
    //     transformer: {
    //         from: () => { },
    //         to: () => { }
    //     }
    // })
    // tends!: any[];
}
