import { Component, Widget } from "@nger/rest";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Component({
    title: '主题截面'
})
@Entity({
    name: 'spilder_topic_tend'
})
export class SpilderTopicTend {

    @Widget({
        title: '序号',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '话题',
        widget: {
            type: 'selector',
            from: 'spilder_topic',
            config: {
                label: 'title',
                value: 'id'
            }
        }
    })
    @Column()
    tid!: number;

    @Widget({
        title: '阅读次数',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    read_count!: number;

    @Widget({
        title: '讨论次数',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    mention_count!: number;

    @Widget({
        title: '原创人数',
        widget: {
            type: 'number'
        },
        hideInSearch: true
    })
    @Column({
        type: 'bigint',
        nullable: true
    })
    ori_uv_Count!: number;

    @Widget({
        title: '爬取时间',
        sortable: true,
        hideInSearch: true,
        hideInAdd: true,
        hideInEdit: true
    })
    @CreateDateColumn({
        name: 'create_date'
    })
    createDate!: Date;
}
