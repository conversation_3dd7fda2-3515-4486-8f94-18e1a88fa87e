import { Component, Widget } from "@nger/rest";
import { Column, Entity, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";

@Component({
    title: '定时任务'
})
@Entity({
    name: 'wb_task'
})
export class WbTask {
    @Widget({
        title: '序号'
    })
    @PrimaryGeneratedColumn()
    id!: number;

    @Widget({
        title: '时间'
    })
    @Column()
    time!: string;

    @Widget({
        title: '事件'
    })
    @Column('simple-array', {
        nullable: true
    })
    ids!: number[];
}
