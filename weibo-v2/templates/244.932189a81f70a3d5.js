"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[244],{1244:(B,r,i)=>{i.r(r),i.d(r,{RestPageModule:()=>I});var l=i(5775),c=i(5708),z=i(4284),m=i(4101),u=i(3984),e=i(485),p=i(5537),h=i(4254),g=i(1529);const d=["nzType","avatar"];function f(n,a){if(1&n&&(e.TgZ(0,"div",5),e._UZ(1,"nz-skeleton-element",6),e.qZA()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("nzSize",t.avatar.size||"default")("nzShape",t.avatar.shape||"circle")}}function k(n,a){if(1&n&&e._UZ(0,"h3",7),2&n){const t=e.oxw(2);e.Udp("width",t.toCSSUnit(t.title.width))}}function S(n,a){if(1&n&&e._UZ(0,"li"),2&n){const t=a.index,o=e.oxw(3);e.Udp("width",o.toCSSUnit(o.widthList[t]))}}function v(n,a){if(1&n&&(e.TgZ(0,"ul",8),e.YNc(1,S,1,2,"li",9),e.qZA()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.rowsList)}}function C(n,a){if(1&n&&(e.ynx(0),e.YNc(1,f,2,2,"div",1),e.TgZ(2,"div",2),e.YNc(3,k,1,2,"h3",3),e.YNc(4,v,2,1,"ul",4),e.qZA(),e.BQk()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",!!t.nzAvatar),e.xp6(2),e.Q6J("ngIf",!!t.nzTitle),e.xp6(1),e.Q6J("ngIf",!!t.nzParagraph)}}function y(n,a){1&n&&(e.ynx(0),e.Hsn(1),e.BQk())}const N=["*"];let T=(()=>{class n{constructor(){this.nzActive=!1,this.nzBlock=!1}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275dir=e.lG2({type:n,selectors:[["nz-skeleton-element"]],hostAttrs:[1,"ant-skeleton","ant-skeleton-element"],hostVars:4,hostBindings:function(t,o){2&t&&e.ekj("ant-skeleton-active",o.nzActive)("ant-skeleton-block",o.nzBlock)},inputs:{nzActive:"nzActive",nzType:"nzType",nzBlock:"nzBlock"}}),(0,h.gn)([(0,p.yF)()],n.prototype,"nzBlock",void 0),n})(),_=(()=>{class n{constructor(){this.nzShape="circle",this.nzSize="default",this.styleMap={}}ngOnChanges(t){if(t.nzSize&&"number"==typeof this.nzSize){const o=`${this.nzSize}px`;this.styleMap={width:o,height:o,"line-height":o}}else this.styleMap={}}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275cmp=e.Xpm({type:n,selectors:[["nz-skeleton-element","nzType","avatar"]],inputs:{nzShape:"nzShape",nzSize:"nzSize"},features:[e.TTD],attrs:d,decls:1,vars:9,consts:[[1,"ant-skeleton-avatar",3,"ngStyle"]],template:function(t,o){1&t&&e._UZ(0,"span",0),2&t&&(e.ekj("ant-skeleton-avatar-square","square"===o.nzShape)("ant-skeleton-avatar-circle","circle"===o.nzShape)("ant-skeleton-avatar-lg","large"===o.nzSize)("ant-skeleton-avatar-sm","small"===o.nzSize),e.Q6J("ngStyle",o.styleMap))},dependencies:[l.PC],encapsulation:2,changeDetection:0}),n})(),P=(()=>{class n{constructor(t,o,s){this.cdr=t,this.nzActive=!1,this.nzLoading=!0,this.nzRound=!1,this.nzTitle=!0,this.nzAvatar=!1,this.nzParagraph=!0,this.rowsList=[],this.widthList=[],o.addClass(s.nativeElement,"ant-skeleton")}toCSSUnit(t=""){return(0,p.WX)(t)}getTitleProps(){const t=!!this.nzAvatar,o=!!this.nzParagraph;let s="";return!t&&o?s="38%":t&&o&&(s="50%"),{width:s,...this.getProps(this.nzTitle)}}getAvatarProps(){return{shape:this.nzTitle&&!this.nzParagraph?"square":"circle",size:"large",...this.getProps(this.nzAvatar)}}getParagraphProps(){const t=!!this.nzAvatar,o=!!this.nzTitle,s={};return(!t||!o)&&(s.width="61%"),s.rows=!t&&o?3:2,{...s,...this.getProps(this.nzParagraph)}}getProps(t){return t&&"object"==typeof t?t:{}}getWidthList(){const{width:t,rows:o}=this.paragraph;let s=[];return t&&Array.isArray(t)?s=t:t&&!Array.isArray(t)&&(s=[],s[o-1]=t),s}updateProps(){this.title=this.getTitleProps(),this.avatar=this.getAvatarProps(),this.paragraph=this.getParagraphProps(),this.rowsList=[...Array(this.paragraph.rows)],this.widthList=this.getWidthList(),this.cdr.markForCheck()}ngOnInit(){this.updateProps()}ngOnChanges(t){(t.nzTitle||t.nzAvatar||t.nzParagraph)&&this.updateProps()}}return n.\u0275fac=function(t){return new(t||n)(e.Y36(e.sBO),e.Y36(e.Qsj),e.Y36(e.SBq))},n.\u0275cmp=e.Xpm({type:n,selectors:[["nz-skeleton"]],hostVars:6,hostBindings:function(t,o){2&t&&e.ekj("ant-skeleton-with-avatar",!!o.nzAvatar)("ant-skeleton-active",o.nzActive)("ant-skeleton-round",!!o.nzRound)},inputs:{nzActive:"nzActive",nzLoading:"nzLoading",nzRound:"nzRound",nzTitle:"nzTitle",nzAvatar:"nzAvatar",nzParagraph:"nzParagraph"},exportAs:["nzSkeleton"],features:[e.TTD],ngContentSelectors:N,decls:2,vars:2,consts:[[4,"ngIf"],["class","ant-skeleton-header",4,"ngIf"],[1,"ant-skeleton-content"],["class","ant-skeleton-title",3,"width",4,"ngIf"],["class","ant-skeleton-paragraph",4,"ngIf"],[1,"ant-skeleton-header"],["nzType","avatar",3,"nzSize","nzShape"],[1,"ant-skeleton-title"],[1,"ant-skeleton-paragraph"],[3,"width",4,"ngFor","ngForOf"]],template:function(t,o){1&t&&(e.F$t(),e.YNc(0,C,5,3,"ng-container",0),e.YNc(1,y,2,0,"ng-container",0)),2&t&&(e.Q6J("ngIf",o.nzLoading),e.xp6(1),e.Q6J("ngIf",!o.nzLoading))},dependencies:[_,l.O5,T,l.sg],encapsulation:2,changeDetection:0}),n})(),A=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[[g.vT,l.ez]]}),n})();function E(n,a){if(1&n){const t=e.EpF();e.TgZ(0,"iframe",2,3),e.NdJ("load",function(s){e.CHM(t);const M=e.oxw();return e.KtG(M.onLoad(s))}),e.qZA()}if(2&n){const t=e.oxw();e.Q6J("src",t.path,e.uOi)}}function w(n,a){1&n&&e._UZ(0,"nz-skeleton")}let x=(()=>{class n{constructor(t){this.injector=t,this.destory$=new m.x,this.loading=!1,this.activatedRoute.queryParams.pipe((0,u.R)(this.destory$)).subscribe({next:o=>{this.loading=!0,this.path=this.dom.bypassSecurityTrustResourceUrl(o.path)}})}get activatedRoute(){return this.injector.get(c.gz)}get dom(){return this.injector.get(z.H7)}ngOnDestroy(){this.destory$.next(),this.destory$.complete()}onLoad(t){this.loading=!1}}return n.\u0275fac=function(t){return new(t||n)(e.Y36(e.zs3))},n.\u0275cmp=e.Xpm({type:n,selectors:[["rest-page"]],decls:2,vars:2,consts:[[3,"src","load",4,"ngIf"],[4,"ngIf"],[3,"src","load"],["iframe",""]],template:function(t,o){1&t&&(e.YNc(0,E,2,1,"iframe",0),e.YNc(1,w,1,0,"nz-skeleton",1)),2&t&&(e.Q6J("ngIf",o.path),e.xp6(1),e.Q6J("ngIf",o.loading))},dependencies:[l.O5,P],styles:["iframe[_ngcontent-%COMP%]{width:100%;height:100%;border:none}"]}),n})(),I=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[l.ez,A,c.Bz.forChild([{path:"",component:x}])]}),n})()}}]);