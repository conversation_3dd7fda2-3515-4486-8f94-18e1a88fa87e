"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[48],{9635:E=>{var A=Object.prototype.hasOwnProperty,h=Object.prototype.toString,S=Object.defineProperty,M=Object.getOwnPropertyDescriptor,f=function(l){return"function"==typeof Array.isArray?Array.isArray(l):"[object Array]"===h.call(l)},F=function(l){if(!l||"[object Object]"!==h.call(l))return!1;var y,a=A.call(l,"constructor"),g=l.constructor&&l.constructor.prototype&&A.call(l.constructor.prototype,"isPrototypeOf");if(l.constructor&&!a&&!g)return!1;for(y in l);return typeof y>"u"||A.call(l,y)},D=function(l,a){S&&"__proto__"===a.name?S(l,a.name,{enumerable:!0,configurable:!0,value:a.newValue,writable:!0}):l[a.name]=a.newValue},N=function(l,a){if("__proto__"===a){if(!A.call(l,a))return;if(M)return M(l,a).value}return l[a]};E.exports=function x(){var l,a,g,y,w,P,d=arguments[0],m=1,X=arguments.length,O=!1;for("boolean"==typeof d&&(O=d,d=arguments[1]||{},m=2),(null==d||"object"!=typeof d&&"function"!=typeof d)&&(d={});m<X;++m)if(null!=(l=arguments[m]))for(a in l)g=N(d,a),d!==(y=N(l,a))&&(O&&y&&(F(y)||(w=f(y)))?(w?(w=!1,P=g&&f(g)?g:[]):P=g&&F(g)?g:{},D(d,{name:a,newValue:x(O,P,y)})):typeof y<"u"&&D(d,{name:a,newValue:y}));return d}},7451:(E,A,h)=>{h.d(A,{be:()=>b});var S=h(5386),M=h(4254),f=h(485),D=h(9635),N=h(5775),x=h(2837),l=h(34),a=h(9712);let T=(()=>{class e{constructor(t){this.doc=t,this.list={},this.cached={},this._notify=new x.X([])}get change(){return this._notify.asObservable().pipe((0,l.B)(),(0,a.h)(t=>0!==t.length))}clear(){this.list={},this.cached={}}load(t){Array.isArray(t)||(t=[t]);const o=[];return t.forEach(n=>{n.endsWith(".js")?o.push(this.loadScript(n)):o.push(this.loadStyle(n))}),Promise.all(o).then(n=>(this._notify.next(n),Promise.resolve(n)))}loadScript(t,o){return new Promise(n=>{if(!0===this.list[t])return void n({...this.cached[t],status:"loading"});this.list[t]=!0;const i=c=>{this.cached[t]=c,n(c),this._notify.next([c])},s=this.doc.createElement("script");s.type="text/javascript",s.src=t,o&&(s.innerHTML=o),s.onload=()=>i({path:t,status:"ok"}),s.onerror=c=>i({path:t,status:"error",error:c}),this.doc.getElementsByTagName("head")[0].appendChild(s)})}loadStyle(t,o="stylesheet",n){return new Promise(i=>{if(!0===this.list[t])return void i(this.cached[t]);this.list[t]=!0;const s=this.doc.createElement("link");s.rel=o,s.type="text/css",s.href=t,n&&(s.innerHTML=n),this.doc.getElementsByTagName("head")[0].appendChild(s);const c={path:t,status:"ok"};this.cached[t]=c,i(c)})}}return e.\u0275fac=function(t){return new(t||e)(f.LFG(N.K0))},e.\u0275prov=f.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();function I(e){return function L(e,r){return(t,o,n)=>{const i=n.value;return n.value=function(...s){const u=this[r?.ngZoneName||"ngZone"];if(!u)return i.call(this,...s);let p;return u[e](()=>{p=i.call(this,...s)}),p},n}}("runOutsideAngular",e)}var G=h(2356);const k=new f.OlP("alain-config",{providedIn:"root",factory:function Z(){return{}}});let _=(()=>{class e{constructor(t){this.config={...t}}get(t,o){const n=this.config[t]||{};return o?{[o]:n[o]}:n}merge(t,...o){return function w(e,r,...t){if(Array.isArray(e)||"object"!=typeof e)return e;const o=i=>"object"==typeof i,n=(i,s)=>(Object.keys(s).filter(c=>"__proto__"!==c&&Object.prototype.hasOwnProperty.call(s,c)).forEach(c=>{const u=s[c],p=i[c];i[c]=Array.isArray(p)?r?u:[...p,...u]:"function"==typeof u?u:null!=u&&o(u)&&null!=p&&o(p)?n(p,u):function y(e){return D(!0,{},{_:e})._}(u)}),i);return t.filter(i=>null!=i&&o(i)).forEach(i=>n(e,i)),e}({},!0,...o,this.get(t))}attach(t,o,n){Object.assign(t,this.merge(o,n))}attachKey(t,o,n){Object.assign(t,this.get(o,n))}set(t,o){this.config[t]={...this.config[t],...o}}}return e.\u0275fac=function(t){return new(t||e)(f.LFG(k,8))},e.\u0275prov=f.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),b=(()=>{class e{constructor(t,o,n,i){this.http=t,this.lazy=o,this.ngZone=i,this.cog=n.merge("xlsx",{url:"https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js",modules:["https://cdn.jsdelivr.net/npm/xlsx/dist/cpexcel.js"]})}init(){return typeof XLSX<"u"?Promise.resolve([]):this.lazy.load([this.cog.url].concat(this.cog.modules))}read(t){const{read:o,utils:{sheet_to_json:n}}=XLSX,i={},s=new Uint8Array(t);let c="array";if(!function F(e){if(!e)return!1;for(var r=0,t=e.length;r<t;)if(e[r]<=127)r++;else{if(e[r]>=194&&e[r]<=223){if(e[r+1]>>6==2){r+=2;continue}return!1}if((224===e[r]&&e[r+1]>=160&&e[r+1]<=191||237===e[r]&&e[r+1]>=128&&e[r+1]<=159)&&e[r+2]>>6==2)r+=3;else if((e[r]>=225&&e[r]<=236||e[r]>=238&&e[r]<=239)&&e[r+1]>>6==2&&e[r+2]>>6==2)r+=3;else{if(!(240===e[r]&&e[r+1]>=144&&e[r+1]<=191||e[r]>=241&&e[r]<=243&&e[r+1]>>6==2||244===e[r]&&e[r+1]>=128&&e[r+1]<=143)||e[r+2]>>6!=2||e[r+3]>>6!=2)return!1;r+=4}}return!0}(s))try{t=cptable.utils.decode(936,s),c="string"}catch{}const u=o(t,{type:c});return u.SheetNames.forEach(p=>{i[p]=n(u.Sheets[p],{header:1})}),i}import(t){return new Promise((o,n)=>{const i=s=>this.ngZone.run(()=>o(this.read(s)));this.init().then(()=>{if("string"==typeof t)return void this.http.request("GET",t,{responseType:"arraybuffer"}).subscribe({next:c=>i(new Uint8Array(c)),error:c=>n(c)});const s=new FileReader;s.onload=c=>i(c.target.result),s.onerror=c=>n(c),s.readAsArrayBuffer(t)}).catch(()=>n("Unable to load xlsx.js"))})}export(t){var o=this;return(0,S.Z)(function*(){return new Promise((n,i)=>{o.init().then(()=>{t={format:"xlsx",...t};const{writeFile:s,utils:{book_new:c,aoa_to_sheet:u,book_append_sheet:p}}=XLSX,v=c();Array.isArray(t.sheets)?t.sheets.forEach((B,z)=>{const H=u(B.data);p(v,H,B.name||`Sheet${z+1}`)}):(v.SheetNames=Object.keys(t.sheets),v.Sheets=t.sheets),t.callback&&t.callback(v);const j=t.filename||`export.${t.format}`;s(v,j,{bookType:t.format,bookSST:!1,type:"array",...t.opts}),n({filename:j,wb:v})}).catch(s=>i(s))})})()}numberToSchema(t){const o="A".charCodeAt(0);let n="";do{--t,n=String.fromCharCode(o+t%26)+n,t=t/26>>0}while(t>0);return n}}return e.\u0275fac=function(t){return new(t||e)(f.LFG(G.eN),f.LFG(T),f.LFG(_),f.LFG(f.R0b))},e.\u0275prov=f.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),(0,M.gn)([I()],e.prototype,"read",null),(0,M.gn)([I()],e.prototype,"export",null),e})()}}]);