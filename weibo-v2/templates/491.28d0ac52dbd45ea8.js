"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[491],{7386:(Yt,ot,r)=>{r.d(ot,{RR:()=>ft,b1:()=>_t,cm:()=>pt});var e=r(4254),S=r(6634),Y=r(6551),l=r(485),w=r(4101),$=r(2837),g=r(5046),A=r(5916),U=r(415),at=r(5334),Q=r(4216),K=r(9426),q=r(7892),x=r(9712),N=r(4343),rt=r(4785),L=r(3984),tt=r(422),R=r(7967),Z=r(5537),M=r(3606),z=r(7335),j=r(1529),G=r(5775),lt=r(3233),J=r(3165),b=r(9461),D=r(8869),V=r(5217),k=r(2002),dt=r(7541);function W(c,_){if(1&c){const d=l.EpF();l.TgZ(0,"div",0),l.NdJ("@slideMotion.done",function(f){l.CHM(d);const C=l.oxw();return l.KtG(C.onAnimationEvent(f))})("mouseenter",function(){l.CHM(d);const f=l.oxw();return l.KtG(f.setMouseState(!0))})("mouseleave",function(){l.CHM(d);const f=l.oxw();return l.KtG(f.setMouseState(!1))}),l.Hsn(1),l.qZA()}if(2&c){const d=l.oxw();l.ekj("ant-dropdown-rtl","rtl"===d.dir),l.Q6J("ngClass",d.nzOverlayClassName)("ngStyle",d.nzOverlayStyle)("@slideMotion",void 0)("@.disabled",null==d.noAnimation?null:d.noAnimation.nzNoAnimation)("nzNoAnimation",null==d.noAnimation?null:d.noAnimation.nzNoAnimation)}}const ct=["*"],ut=[R.yW.bottomLeft,R.yW.bottomRight,R.yW.topRight,R.yW.topLeft];let pt=(()=>{class c{constructor(d,p,f,C,E,y){this.nzConfigService=d,this.elementRef=p,this.overlay=f,this.renderer=C,this.viewContainerRef=E,this.platform=y,this._nzModuleName="dropDown",this.overlayRef=null,this.destroy$=new w.x,this.positionStrategy=this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn(".ant-dropdown"),this.inputVisible$=new $.X(!1),this.nzTrigger$=new $.X("hover"),this.overlayClose$=new w.x,this.nzDropdownMenu=null,this.nzTrigger="hover",this.nzMatchWidthElement=null,this.nzBackdrop=!1,this.nzClickHide=!0,this.nzDisabled=!1,this.nzVisible=!1,this.nzOverlayClassName="",this.nzOverlayStyle={},this.nzPlacement="bottomLeft",this.nzVisibleChange=new l.vpe}setDropdownMenuValue(d,p){this.nzDropdownMenu&&this.nzDropdownMenu.setValue(d,p)}ngAfterViewInit(){if(this.nzDropdownMenu){const d=this.elementRef.nativeElement,p=(0,g.T)((0,A.R)(d,"mouseenter").pipe((0,Q.h)(!0)),(0,A.R)(d,"mouseleave").pipe((0,Q.h)(!1))),C=(0,g.T)(this.nzDropdownMenu.mouseState$,p),E=(0,A.R)(d,"click").pipe((0,K.U)(()=>!this.nzVisible)),y=this.nzTrigger$.pipe((0,q.w)(T=>"hover"===T?C:"click"===T?E:U.E)),P=this.nzDropdownMenu.descendantMenuItemClick$.pipe((0,x.h)(()=>this.nzClickHide),(0,Q.h)(!1)),bt=(0,g.T)(y,P,this.overlayClose$).pipe((0,x.h)(()=>!this.nzDisabled)),vt=(0,g.T)(this.inputVisible$,bt);(0,at.a)([vt,this.nzDropdownMenu.isChildSubMenuOpen$]).pipe((0,K.U)(([T,et])=>T||et),(0,N.e)(150),(0,rt.x)(),(0,x.h)(()=>this.platform.isBrowser),(0,L.R)(this.destroy$)).subscribe(T=>{const nt=(this.nzMatchWidthElement?this.nzMatchWidthElement.nativeElement:d).getBoundingClientRect().width;this.nzVisible!==T&&this.nzVisibleChange.emit(T),this.nzVisible=T,T?(this.overlayRef?this.overlayRef.getConfig().minWidth=nt:(this.overlayRef=this.overlay.create({positionStrategy:this.positionStrategy,minWidth:nt,disposeOnNavigation:!0,hasBackdrop:this.nzBackdrop&&"click"===this.nzTrigger,scrollStrategy:this.overlay.scrollStrategies.reposition()}),(0,g.T)(this.overlayRef.backdropClick(),this.overlayRef.detachments(),this.overlayRef.outsidePointerEvents().pipe((0,x.h)(B=>!this.elementRef.nativeElement.contains(B.target))),this.overlayRef.keydownEvents().pipe((0,x.h)(B=>B.keyCode===S.hY&&!(0,S.Vb)(B)))).pipe((0,L.R)(this.destroy$)).subscribe(()=>{this.overlayClose$.next(!1)})),this.positionStrategy.withPositions([R.yW[this.nzPlacement],...ut]),(!this.portal||this.portal.templateRef!==this.nzDropdownMenu.templateRef)&&(this.portal=new Y.UE(this.nzDropdownMenu.templateRef,this.viewContainerRef)),this.overlayRef.attach(this.portal)):this.overlayRef&&this.overlayRef.detach()}),this.nzDropdownMenu.animationStateChange$.pipe((0,L.R)(this.destroy$)).subscribe(T=>{"void"===T.toState&&(this.overlayRef&&this.overlayRef.dispose(),this.overlayRef=null)})}}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.overlayRef&&(this.overlayRef.dispose(),this.overlayRef=null)}ngOnChanges(d){const{nzVisible:p,nzDisabled:f,nzOverlayClassName:C,nzOverlayStyle:E,nzTrigger:y}=d;if(y&&this.nzTrigger$.next(this.nzTrigger),p&&this.inputVisible$.next(this.nzVisible),f){const P=this.elementRef.nativeElement;this.nzDisabled?(this.renderer.setAttribute(P,"disabled",""),this.inputVisible$.next(!1)):this.renderer.removeAttribute(P,"disabled")}C&&this.setDropdownMenuValue("nzOverlayClassName",this.nzOverlayClassName),E&&this.setDropdownMenuValue("nzOverlayStyle",this.nzOverlayStyle)}}return c.\u0275fac=function(d){return new(d||c)(l.Y36(tt.jY),l.Y36(l.SBq),l.Y36(M.aV),l.Y36(l.Qsj),l.Y36(l.s_b),l.Y36(z.t4))},c.\u0275dir=l.lG2({type:c,selectors:[["","nz-dropdown",""]],hostAttrs:[1,"ant-dropdown-trigger"],inputs:{nzDropdownMenu:"nzDropdownMenu",nzTrigger:"nzTrigger",nzMatchWidthElement:"nzMatchWidthElement",nzBackdrop:"nzBackdrop",nzClickHide:"nzClickHide",nzDisabled:"nzDisabled",nzVisible:"nzVisible",nzOverlayClassName:"nzOverlayClassName",nzOverlayStyle:"nzOverlayStyle",nzPlacement:"nzPlacement"},outputs:{nzVisibleChange:"nzVisibleChange"},exportAs:["nzDropdown"],features:[l.TTD]}),(0,e.gn)([(0,tt.oS)(),(0,Z.yF)()],c.prototype,"nzBackdrop",void 0),(0,e.gn)([(0,Z.yF)()],c.prototype,"nzClickHide",void 0),(0,e.gn)([(0,Z.yF)()],c.prototype,"nzDisabled",void 0),(0,e.gn)([(0,Z.yF)()],c.prototype,"nzVisible",void 0),c})(),mt=(()=>{class c{}return c.\u0275fac=function(d){return new(d||c)},c.\u0275mod=l.oAB({type:c}),c.\u0275inj=l.cJS({}),c})(),ft=(()=>{class c{constructor(d,p,f,C,E,y,P){this.cdr=d,this.elementRef=p,this.renderer=f,this.viewContainerRef=C,this.nzMenuService=E,this.directionality=y,this.noAnimation=P,this.mouseState$=new $.X(!1),this.isChildSubMenuOpen$=this.nzMenuService.isChildSubMenuOpen$,this.descendantMenuItemClick$=this.nzMenuService.descendantMenuItemClick$,this.animationStateChange$=new l.vpe,this.nzOverlayClassName="",this.nzOverlayStyle={},this.dir="ltr",this.destroy$=new w.x}onAnimationEvent(d){this.animationStateChange$.emit(d)}setMouseState(d){this.mouseState$.next(d)}setValue(d,p){this[d]=p,this.cdr.markForCheck()}ngOnInit(){this.directionality.change?.pipe((0,L.R)(this.destroy$)).subscribe(d=>{this.dir=d,this.cdr.detectChanges()}),this.dir=this.directionality.value}ngAfterContentInit(){this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement),this.elementRef.nativeElement)}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}}return c.\u0275fac=function(d){return new(d||c)(l.Y36(l.sBO),l.Y36(l.SBq),l.Y36(l.Qsj),l.Y36(l.s_b),l.Y36(k.hl),l.Y36(j.Is,8),l.Y36(b.P,9))},c.\u0275cmp=l.Xpm({type:c,selectors:[["nz-dropdown-menu"]],viewQuery:function(d,p){if(1&d&&l.Gf(l.Rgc,7),2&d){let f;l.iGM(f=l.CRH())&&(p.templateRef=f.first)}},exportAs:["nzDropdownMenu"],features:[l._Bn([k.hl,{provide:k.Cc,useValue:!0}])],ngContentSelectors:ct,decls:1,vars:0,consts:[[1,"ant-dropdown",3,"ngClass","ngStyle","nzNoAnimation","mouseenter","mouseleave"]],template:function(d,p){1&d&&(l.F$t(),l.YNc(0,W,2,7,"ng-template"))},dependencies:[G.mk,G.PC,b.P],encapsulation:2,data:{animation:[dt.mF]},changeDetection:0}),c})(),_t=(()=>{class c{}return c.\u0275fac=function(d){return new(d||c)},c.\u0275mod=l.oAB({type:c}),c.\u0275inj=l.cJS({imports:[[j.vT,G.ez,M.U8,lt.u5,J.sL,k.ip,V.PV,b.g,z.ud,R.e4,mt,D.T],k.ip]}),c})();new M.tR({originX:"start",originY:"top"},{overlayX:"start",overlayY:"top"}),new M.tR({originX:"start",originY:"top"},{overlayX:"start",overlayY:"bottom"}),new M.tR({originX:"start",originY:"top"},{overlayX:"end",overlayY:"bottom"}),new M.tR({originX:"start",originY:"top"},{overlayX:"end",overlayY:"top"})},8491:(Yt,ot,r)=>{r.d(ot,{we:()=>jt,xH:()=>Wt,xw:()=>Dt});var e=r(485),S=r(8869),Y=r(5217),l=r(6629),w=r(5539),$=r(182),g=r(6634),A=r(5916),U=r(6595),at=r(9371),Q=r(511),K=r(4101),q=r(4383),x=r(5046),N=r(3984),rt=r(4343),L=r(1174),tt=r(8265),R=r(9712),Z=r(8523),M=r(7386),z=r(5775),j=r(2002),G=r(6246),lt=r(6907),J=r(1529),b=r(4254),D=r(5537),V=r(5708),k=r(3442),dt=r(7335),W=r(422),ct=r(5655);function ht(i,a){if(1&i&&(e.ynx(0),e._UZ(1,"i",1),e.BQk()),2&i){const t=a.$implicit;e.xp6(1),e.Q6J("nzType",t)}}function ut(i,a){if(1&i&&(e.ynx(0),e._uU(1),e.BQk()),2&i){const t=e.oxw().$implicit;e.xp6(1),e.hij(" ",t.tab.label," ")}}const pt=function(){return{visible:!1}};function mt(i,a){if(1&i){const t=e.EpF();e.TgZ(0,"li",8),e.NdJ("click",function(){const o=e.CHM(t).$implicit,h=e.oxw(2);return e.KtG(h.onSelect(o))})("contextmenu",function(s){const h=e.CHM(t).$implicit,u=e.oxw(2);return e.KtG(u.onContextmenu(h,s))}),e.YNc(1,ut,2,1,"ng-container",9),e.qZA()}if(2&i){const t=a.$implicit;e.ekj("ant-tabs-dropdown-menu-item-disabled",t.disabled),e.Q6J("nzSelected",t.active)("nzDisabled",t.disabled),e.xp6(1),e.Q6J("nzStringTemplateOutlet",t.tab.label)("nzStringTemplateOutletContext",e.DdM(6,pt))}}function Mt(i,a){if(1&i&&(e.TgZ(0,"ul",6),e.YNc(1,mt,2,7,"li",7),e.qZA()),2&i){const t=e.oxw();e.xp6(1),e.Q6J("ngForOf",t.items)}}function Et(i,a){if(1&i){const t=e.EpF();e.TgZ(0,"button",10),e.NdJ("click",function(){e.CHM(t);const s=e.oxw();return e.KtG(s.addClicked.emit())}),e.qZA()}if(2&i){const t=e.oxw();e.Q6J("addIcon",t.addIcon)}}const ft=function(){return{minWidth:"46px"}},_t=["navWarp"],It=["navList"];function xt(i,a){if(1&i){const t=e.EpF();e.TgZ(0,"button",8),e.NdJ("click",function(){e.CHM(t);const s=e.oxw();return e.KtG(s.addClicked.emit())}),e.qZA()}if(2&i){const t=e.oxw();e.Q6J("addIcon",t.addIcon)}}function c(i,a){}function _(i,a){if(1&i&&(e.TgZ(0,"div",9),e.YNc(1,c,0,0,"ng-template",10),e.qZA()),2&i){const t=e.oxw();e.xp6(1),e.Q6J("ngTemplateOutlet",t.extraTemplate)}}const d=["*"],p=["nz-tab-body",""];function f(i,a){}function C(i,a){if(1&i&&(e.ynx(0),e.YNc(1,f,0,0,"ng-template",1),e.BQk()),2&i){const t=e.oxw();e.xp6(1),e.Q6J("ngTemplateOutlet",t.content)}}function E(i,a){if(1&i&&(e.ynx(0),e._UZ(1,"i",1),e.BQk()),2&i){const t=a.$implicit;e.xp6(1),e.Q6J("nzType",t)}}const y=["contentTemplate"];function P(i,a){1&i&&e.Hsn(0)}function bt(i,a){1&i&&e.Hsn(0,1)}const vt=[[["","nz-tab-link",""]],"*"],T=["[nz-tab-link]","*"];function et(i,a){if(1&i&&(e.ynx(0),e._uU(1),e.BQk()),2&i){const t=e.oxw().$implicit;e.xp6(1),e.Oqu(t.label)}}function nt(i,a){if(1&i){const t=e.EpF();e.TgZ(0,"button",10),e.NdJ("click",function(s){e.CHM(t);const o=e.oxw().index,h=e.oxw(2);return e.KtG(h.onClose(o,s))}),e.qZA()}if(2&i){const t=e.oxw().$implicit;e.Q6J("closeIcon",t.nzCloseIcon)}}const B=function(){return{visible:!0}};function $t(i,a){if(1&i){const t=e.EpF();e.TgZ(0,"div",6),e.NdJ("click",function(s){const o=e.CHM(t),h=o.$implicit,u=o.index,m=e.oxw(2);return e.KtG(m.clickNavItem(h,u,s))})("contextmenu",function(s){const h=e.CHM(t).$implicit,u=e.oxw(2);return e.KtG(u.contextmenuNavItem(h,s))}),e.TgZ(1,"div",7),e.YNc(2,et,2,1,"ng-container",8),e.YNc(3,nt,1,1,"button",9),e.qZA()()}if(2&i){const t=a.$implicit,n=a.index,s=e.oxw(2);e.Udp("margin-right","horizontal"===s.position?s.nzTabBarGutter:null,"px")("margin-bottom","vertical"===s.position?s.nzTabBarGutter:null,"px"),e.ekj("ant-tabs-tab-active",s.nzSelectedIndex===n)("ant-tabs-tab-disabled",t.nzDisabled),e.xp6(1),e.Q6J("disabled",t.nzDisabled)("tab",t)("active",s.nzSelectedIndex===n),e.uIk("tabIndex",s.getTabIndex(t,n))("aria-disabled",t.nzDisabled)("aria-selected",s.nzSelectedIndex===n&&!s.nzHideAll)("aria-controls",s.getTabContentId(n)),e.xp6(1),e.Q6J("nzStringTemplateOutlet",t.label)("nzStringTemplateOutletContext",e.DdM(18,B)),e.xp6(1),e.Q6J("ngIf",t.nzClosable&&s.closable&&!t.nzDisabled)}}function Ut(i,a){if(1&i){const t=e.EpF();e.TgZ(0,"nz-tabs-nav",4),e.NdJ("tabScroll",function(s){e.CHM(t);const o=e.oxw();return e.KtG(o.nzTabListScroll.emit(s))})("selectFocusedIndex",function(s){e.CHM(t);const o=e.oxw();return e.KtG(o.setSelectedIndex(s))})("addClicked",function(){e.CHM(t);const s=e.oxw();return e.KtG(s.onAdd())}),e.YNc(1,$t,4,19,"div",5),e.qZA()}if(2&i){const t=e.oxw();e.Q6J("ngStyle",t.nzTabBarStyle)("selectedIndex",t.nzSelectedIndex||0)("inkBarAnimated",t.inkBarAnimated)("addable",t.addable)("addIcon",t.nzAddIcon)("hideBar",t.nzHideAll)("position",t.position)("extraTemplate",t.nzTabBarExtraContent),e.xp6(1),e.Q6J("ngForOf",t.tabs)}}function Kt(i,a){if(1&i&&e._UZ(0,"div",11),2&i){const t=a.$implicit,n=a.index,s=e.oxw();e.Q6J("active",s.nzSelectedIndex===n&&!s.nzHideAll)("content",t.content)("forceRender",t.nzForceRender)("tabPaneAnimated",s.tabPaneAnimated)}}let it=(()=>{class i{constructor(t){this.elementRef=t,this.addIcon="plus",this.element=this.elementRef.nativeElement}getElementWidth(){return this.element?.offsetWidth||0}getElementHeight(){return this.element?.offsetHeight||0}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.SBq))},i.\u0275cmp=e.Xpm({type:i,selectors:[["nz-tab-add-button"],["button","nz-tab-add-button",""]],hostAttrs:["aria-label","Add tab","type","button",1,"ant-tabs-nav-add"],inputs:{addIcon:"addIcon"},decls:1,vars:1,consts:[[4,"nzStringTemplateOutlet"],["nz-icon","","nzTheme","outline",3,"nzType"]],template:function(t,n){1&t&&e.YNc(0,ht,2,1,"ng-container",0),2&t&&e.Q6J("nzStringTemplateOutlet",n.addIcon)},dependencies:[S.f,Y.Ls],encapsulation:2}),i})(),gt=(()=>{class i{constructor(t,n,s){this.elementRef=t,this.ngZone=n,this.animationMode=s,this.position="horizontal",this.animated=!0}get _animated(){return"NoopAnimations"!==this.animationMode&&this.animated}alignToElement(t){this.ngZone.runOutsideAngular(()=>{(0,l.e)(()=>this.setStyles(t))})}setStyles(t){const n=this.elementRef.nativeElement;"horizontal"===this.position?(n.style.top="",n.style.height="",n.style.left=this.getLeftPosition(t),n.style.width=this.getElementWidth(t)):(n.style.left="",n.style.width="",n.style.top=this.getTopPosition(t),n.style.height=this.getElementHeight(t))}getLeftPosition(t){return t?`${t.offsetLeft||0}px`:"0"}getElementWidth(t){return t?`${t.offsetWidth||0}px`:"0"}getTopPosition(t){return t?`${t.offsetTop||0}px`:"0"}getElementHeight(t){return t?`${t.offsetHeight||0}px`:"0"}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.SBq),e.Y36(e.R0b),e.Y36(e.QbO,8))},i.\u0275dir=e.lG2({type:i,selectors:[["nz-tabs-ink-bar"],["","nz-tabs-ink-bar",""]],hostAttrs:[1,"ant-tabs-ink-bar"],hostVars:2,hostBindings:function(t,n){2&t&&e.ekj("ant-tabs-ink-bar-animated",n._animated)},inputs:{position:"position",animated:"animated"}}),i})(),zt=(()=>{class i{constructor(t){this.elementRef=t,this.disabled=!1,this.active=!1,this.el=t.nativeElement,this.parentElement=this.el.parentElement}focus(){this.el.focus()}get width(){return this.parentElement.offsetWidth}get height(){return this.parentElement.offsetHeight}get left(){return this.parentElement.offsetLeft}get top(){return this.parentElement.offsetTop}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.SBq))},i.\u0275dir=e.lG2({type:i,selectors:[["","nzTabNavItem",""]],inputs:{disabled:"disabled",tab:"tab",active:"active"}}),i})(),Tt=(()=>{class i{constructor(t,n){this.cdr=t,this.elementRef=n,this.items=[],this.addable=!1,this.addIcon="plus",this.addClicked=new e.vpe,this.selected=new e.vpe,this.closeAnimationWaitTimeoutId=-1,this.menuOpened=!1,this.element=this.elementRef.nativeElement}onSelect(t){t.disabled||(t.tab.nzClick.emit(),this.selected.emit(t))}onContextmenu(t,n){t.disabled||t.tab.nzContextmenu.emit(n)}showItems(){clearTimeout(this.closeAnimationWaitTimeoutId),this.menuOpened=!0,this.cdr.markForCheck()}menuVisChange(t){t||(this.closeAnimationWaitTimeoutId=setTimeout(()=>{this.menuOpened=!1,this.cdr.markForCheck()},150))}getElementWidth(){return this.element?.offsetWidth||0}getElementHeight(){return this.element?.offsetHeight||0}ngOnDestroy(){clearTimeout(this.closeAnimationWaitTimeoutId)}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.sBO),e.Y36(e.SBq))},i.\u0275cmp=e.Xpm({type:i,selectors:[["nz-tab-nav-operation"]],hostAttrs:[1,"ant-tabs-nav-operations"],hostVars:2,hostBindings:function(t,n){2&t&&e.ekj("ant-tabs-nav-operations-hidden",0===n.items.length)},inputs:{items:"items",addable:"addable",addIcon:"addIcon"},outputs:{addClicked:"addClicked",selected:"selected"},exportAs:["nzTabNavOperation"],decls:7,vars:6,consts:[["nz-dropdown","","type","button","tabindex","-1","aria-hidden","true","nzOverlayClassName","nz-tabs-dropdown",1,"ant-tabs-nav-more",3,"nzDropdownMenu","nzOverlayStyle","nzMatchWidthElement","nzVisibleChange","mouseenter"],["dropdownTrigger","nzDropdown"],["nz-icon","","nzType","ellipsis"],["menu","nzDropdownMenu"],["nz-menu","",4,"ngIf"],["nz-tab-add-button","",3,"addIcon","click",4,"ngIf"],["nz-menu",""],["nz-menu-item","","class","ant-tabs-dropdown-menu-item",3,"ant-tabs-dropdown-menu-item-disabled","nzSelected","nzDisabled","click","contextmenu",4,"ngFor","ngForOf"],["nz-menu-item","",1,"ant-tabs-dropdown-menu-item",3,"nzSelected","nzDisabled","click","contextmenu"],[4,"nzStringTemplateOutlet","nzStringTemplateOutletContext"],["nz-tab-add-button","",3,"addIcon","click"]],template:function(t,n){if(1&t&&(e.TgZ(0,"button",0,1),e.NdJ("nzVisibleChange",function(o){return n.menuVisChange(o)})("mouseenter",function(){return n.showItems()}),e._UZ(2,"i",2),e.qZA(),e.TgZ(3,"nz-dropdown-menu",null,3),e.YNc(5,Mt,2,1,"ul",4),e.qZA(),e.YNc(6,Et,1,1,"button",5)),2&t){const s=e.MAs(4);e.Q6J("nzDropdownMenu",s)("nzOverlayStyle",e.DdM(5,ft))("nzMatchWidthElement",null),e.xp6(5),e.Q6J("ngIf",n.menuOpened),e.xp6(1),e.Q6J("ngIf",n.addable)}},dependencies:[M.RR,it,M.cm,Y.Ls,z.O5,j.wO,z.sg,j.r9,S.f],encapsulation:2,changeDetection:0}),i})();const St=.995**20;let At=(()=>{class i{constructor(t,n){this.ngZone=t,this.elementRef=n,this.lastWheelDirection=null,this.lastWheelTimestamp=0,this.lastTimestamp=0,this.lastTimeDiff=0,this.lastMixedWheel=0,this.lastWheelPrevent=!1,this.touchPosition=null,this.lastOffset=null,this.motion=-1,this.unsubscribe=()=>{},this.offsetChange=new e.vpe,this.tabScroll=new e.vpe,this.onTouchEnd=s=>{if(!this.touchPosition)return;const o=this.lastOffset,h=this.lastTimeDiff;if(this.lastOffset=this.touchPosition=null,o){const u=o.x/h,m=o.y/h,I=Math.abs(u),H=Math.abs(m);if(Math.max(I,H)<.1)return;let O=u,F=m;this.motion=window.setInterval(()=>{Math.abs(O)<.01&&Math.abs(F)<.01?window.clearInterval(this.motion):(O*=St,F*=St,this.onOffset(20*O,20*F,s))},20)}},this.onTouchMove=s=>{if(!this.touchPosition)return;s.preventDefault();const{screenX:o,screenY:h}=s.touches[0],u=o-this.touchPosition.x,m=h-this.touchPosition.y;this.onOffset(u,m,s);const I=Date.now();this.lastTimeDiff=I-this.lastTimestamp,this.lastTimestamp=I,this.lastOffset={x:u,y:m},this.touchPosition={x:o,y:h}},this.onTouchStart=s=>{const{screenX:o,screenY:h}=s.touches[0];this.touchPosition={x:o,y:h},window.clearInterval(this.motion)},this.onWheel=s=>{const{deltaX:o,deltaY:h}=s;let u;const m=Math.abs(o),I=Math.abs(h);m===I?u="x"===this.lastWheelDirection?o:h:m>I?(u=o,this.lastWheelDirection="x"):(u=h,this.lastWheelDirection="y");const H=Date.now(),O=Math.abs(u);(H-this.lastWheelTimestamp>100||O-this.lastMixedWheel>10)&&(this.lastWheelPrevent=!1),this.onOffset(-u,-u,s),(s.defaultPrevented||this.lastWheelPrevent)&&(this.lastWheelPrevent=!0),this.lastWheelTimestamp=H,this.lastMixedWheel=O}}ngOnInit(){this.unsubscribe=this.ngZone.runOutsideAngular(()=>{const t=this.elementRef.nativeElement,n=(0,A.R)(t,"wheel"),s=(0,A.R)(t,"touchstart"),o=(0,A.R)(t,"touchmove"),h=(0,A.R)(t,"touchend"),u=new U.w0;return u.add(this.subscribeWrap("wheel",n,this.onWheel)),u.add(this.subscribeWrap("touchstart",s,this.onTouchStart)),u.add(this.subscribeWrap("touchmove",o,this.onTouchMove)),u.add(this.subscribeWrap("touchend",h,this.onTouchEnd)),()=>{u.unsubscribe()}})}subscribeWrap(t,n,s){return n.subscribe(o=>{this.tabScroll.emit({type:t,event:o}),o.defaultPrevented||s(o)})}onOffset(t,n,s){this.ngZone.run(()=>{this.offsetChange.emit({x:t,y:n,event:s})})}ngOnDestroy(){this.unsubscribe()}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.R0b),e.Y36(e.SBq))},i.\u0275dir=e.lG2({type:i,selectors:[["","nzTabScrollList",""]],outputs:{offsetChange:"offsetChange",tabScroll:"tabScroll"}}),i})();const Gt=typeof requestAnimationFrame<"u"?at.Z:Q.E;let Ct=(()=>{class i{constructor(t,n,s,o,h){this.cdr=t,this.ngZone=n,this.viewportRuler=s,this.nzResizeObserver=o,this.dir=h,this.indexFocused=new e.vpe,this.selectFocusedIndex=new e.vpe,this.addClicked=new e.vpe,this.tabScroll=new e.vpe,this.position="horizontal",this.addable=!1,this.hideBar=!1,this.addIcon="plus",this.inkBarAnimated=!0,this.translate=null,this.transformX=0,this.transformY=0,this.pingLeft=!1,this.pingRight=!1,this.pingTop=!1,this.pingBottom=!1,this.hiddenItems=[],this.destroy$=new K.x,this._selectedIndex=0,this.wrapperWidth=0,this.wrapperHeight=0,this.scrollListWidth=0,this.scrollListHeight=0,this.operationWidth=0,this.operationHeight=0,this.addButtonWidth=0,this.addButtonHeight=0,this.selectedIndexChanged=!1,this.lockAnimationTimeoutId=-1,this.cssTransformTimeWaitingId=-1}get selectedIndex(){return this._selectedIndex}set selectedIndex(t){const n=(0,$.su)(t);this._selectedIndex!==n&&(this._selectedIndex=t,this.selectedIndexChanged=!0,this.keyManager&&this.keyManager.updateActiveItem(t))}get focusIndex(){return this.keyManager?this.keyManager.activeItemIndex:0}set focusIndex(t){!this.isValidIndex(t)||this.focusIndex===t||!this.keyManager||this.keyManager.setActiveItem(t)}get showAddButton(){return 0===this.hiddenItems.length&&this.addable}ngAfterViewInit(){const t=this.dir?this.dir.change:(0,q.of)(null),n=this.viewportRuler.change(150),s=()=>{this.updateScrollListPosition(),this.alignInkBarToSelectedTab()};this.keyManager=new w.Em(this.items).withHorizontalOrientation(this.getLayoutDirection()).withWrap(),this.keyManager.updateActiveItem(this.selectedIndex),(0,l.e)(s),(0,x.T)(this.nzResizeObserver.observe(this.navWarpRef),this.nzResizeObserver.observe(this.navListRef)).pipe((0,N.R)(this.destroy$),(0,rt.e)(16,Gt)).subscribe(()=>{s()}),(0,x.T)(t,n,this.items.changes).pipe((0,N.R)(this.destroy$)).subscribe(()=>{Promise.resolve().then(s),this.keyManager.withHorizontalOrientation(this.getLayoutDirection())}),this.keyManager.change.pipe((0,N.R)(this.destroy$)).subscribe(o=>{this.indexFocused.emit(o),this.setTabFocus(o),this.scrollToTab(this.keyManager.activeItem)})}ngAfterContentChecked(){this.selectedIndexChanged&&(this.updateScrollListPosition(),this.alignInkBarToSelectedTab(),this.selectedIndexChanged=!1,this.cdr.markForCheck())}ngOnDestroy(){clearTimeout(this.lockAnimationTimeoutId),clearTimeout(this.cssTransformTimeWaitingId),this.destroy$.next(),this.destroy$.complete()}onSelectedFromMenu(t){const n=this.items.toArray().findIndex(s=>s===t);-1!==n&&(this.keyManager.updateActiveItem(n),this.focusIndex!==this.selectedIndex&&(this.selectFocusedIndex.emit(this.focusIndex),this.scrollToTab(t)))}onOffsetChange(t){if("horizontal"===this.position){if(-1===this.lockAnimationTimeoutId&&(this.transformX>=0&&t.x>0||this.transformX<=this.wrapperWidth-this.scrollListWidth&&t.x<0))return;t.event.preventDefault(),this.transformX=this.clampTransformX(this.transformX+t.x),this.setTransform(this.transformX,0)}else{if(-1===this.lockAnimationTimeoutId&&(this.transformY>=0&&t.y>0||this.transformY<=this.wrapperHeight-this.scrollListHeight&&t.y<0))return;t.event.preventDefault(),this.transformY=this.clampTransformY(this.transformY+t.y),this.setTransform(0,this.transformY)}this.lockAnimation(),this.setVisibleRange(),this.setPingStatus()}handleKeydown(t){const n=this.navWarpRef.nativeElement.contains(t.target);if(!(0,g.Vb)(t)&&n)switch(t.keyCode){case g.oh:case g.LH:case g.SV:case g.JH:this.lockAnimation(),this.keyManager.onKeydown(t);break;case g.K5:case g.L_:this.focusIndex!==this.selectedIndex&&this.selectFocusedIndex.emit(this.focusIndex);break;default:this.keyManager.onKeydown(t)}}isValidIndex(t){if(!this.items)return!0;const n=this.items?this.items.toArray()[t]:null;return!!n&&!n.disabled}scrollToTab(t){if(!this.items.find(s=>s===t))return;const n=this.items.toArray();if("horizontal"===this.position){let s=this.transformX;if("rtl"===this.getLayoutDirection()){const o=n[0].left+n[0].width-t.left-t.width;o<this.transformX?s=o:o+t.width>this.transformX+this.wrapperWidth&&(s=o+t.width-this.wrapperWidth)}else t.left<-this.transformX?s=-t.left:t.left+t.width>-this.transformX+this.wrapperWidth&&(s=-(t.left+t.width-this.wrapperWidth));this.transformX=s,this.transformY=0,this.setTransform(s,0)}else{let s=this.transformY;t.top<-this.transformY?s=-t.top:t.top+t.height>-this.transformY+this.wrapperHeight&&(s=-(t.top+t.height-this.wrapperHeight)),this.transformY=s,this.transformX=0,this.setTransform(0,s)}clearTimeout(this.cssTransformTimeWaitingId),this.cssTransformTimeWaitingId=setTimeout(()=>{this.setVisibleRange()},150)}lockAnimation(){-1===this.lockAnimationTimeoutId&&this.ngZone.runOutsideAngular(()=>{this.navListRef.nativeElement.style.transition="none",this.lockAnimationTimeoutId=setTimeout(()=>{this.navListRef.nativeElement.style.transition="",this.lockAnimationTimeoutId=-1},150)})}setTransform(t,n){this.navListRef.nativeElement.style.transform=`translate(${t}px, ${n}px)`}clampTransformX(t){const n=this.wrapperWidth-this.scrollListWidth;return"rtl"===this.getLayoutDirection()?Math.max(Math.min(n,t),0):Math.min(Math.max(n,t),0)}clampTransformY(t){return Math.min(Math.max(this.wrapperHeight-this.scrollListHeight,t),0)}updateScrollListPosition(){this.resetSizes(),this.transformX=this.clampTransformX(this.transformX),this.transformY=this.clampTransformY(this.transformY),this.setVisibleRange(),this.setPingStatus(),this.keyManager&&(this.keyManager.updateActiveItem(this.keyManager.activeItemIndex),this.keyManager.activeItem&&this.scrollToTab(this.keyManager.activeItem))}resetSizes(){this.addButtonWidth=this.addBtnRef?this.addBtnRef.getElementWidth():0,this.addButtonHeight=this.addBtnRef?this.addBtnRef.getElementHeight():0,this.operationWidth=this.operationRef.getElementWidth(),this.operationHeight=this.operationRef.getElementHeight(),this.wrapperWidth=this.navWarpRef.nativeElement.offsetWidth||0,this.wrapperHeight=this.navWarpRef.nativeElement.offsetHeight||0,this.scrollListHeight=this.navListRef.nativeElement.offsetHeight||0,this.scrollListWidth=this.navListRef.nativeElement.offsetWidth||0}alignInkBarToSelectedTab(){const t=this.items&&this.items.length?this.items.toArray()[this.selectedIndex]:null,n=t?t.elementRef.nativeElement:null;n&&this.inkBar.alignToElement(n.parentElement)}setPingStatus(){const t={top:!1,right:!1,bottom:!1,left:!1},n=this.navWarpRef.nativeElement;"horizontal"===this.position?"rtl"===this.getLayoutDirection()?(t.right=this.transformX>0,t.left=this.transformX+this.wrapperWidth<this.scrollListWidth):(t.left=this.transformX<0,t.right=-this.transformX+this.wrapperWidth<this.scrollListWidth):(t.top=this.transformY<0,t.bottom=-this.transformY+this.wrapperHeight<this.scrollListHeight),Object.keys(t).forEach(s=>{const o=`ant-tabs-nav-wrap-ping-${s}`;t[s]?n.classList.add(o):n.classList.remove(o)})}setVisibleRange(){let t,n,s,o,h,u;const m=this.items.toArray(),I={width:0,height:0,left:0,top:0,right:0},H=v=>{let X;return X="right"===n?m[0].left+m[0].width-m[v].left-m[v].width:(m[v]||I)[n],X};"horizontal"===this.position?(t="width",o=this.wrapperWidth,h=this.scrollListWidth-(this.hiddenItems.length?this.operationWidth:0),u=this.addButtonWidth,s=Math.abs(this.transformX),"rtl"===this.getLayoutDirection()?(n="right",this.pingRight=this.transformX>0,this.pingLeft=this.transformX+this.wrapperWidth<this.scrollListWidth):(this.pingLeft=this.transformX<0,this.pingRight=-this.transformX+this.wrapperWidth<this.scrollListWidth,n="left")):(t="height",o=this.wrapperHeight,h=this.scrollListHeight-(this.hiddenItems.length?this.operationHeight:0),u=this.addButtonHeight,n="top",s=-this.transformY,this.pingTop=this.transformY<0,this.pingBottom=-this.transformY+this.wrapperHeight<this.scrollListHeight);let O=o;if(h+u>o&&(O=o-u),!m.length)return this.hiddenItems=[],void this.cdr.markForCheck();const F=m.length;let Ht=F;for(let v=0;v<F;v+=1)if(H(v)+(m[v]||I)[t]>s+O){Ht=v-1;break}let Ft=0;for(let v=F-1;v>=0;v-=1)if(H(v)<s){Ft=v+1;break}const Jt=m.slice(0,Ft),qt=m.slice(Ht+1);this.hiddenItems=[...Jt,...qt],this.cdr.markForCheck()}getLayoutDirection(){return this.dir&&"rtl"===this.dir.value?"rtl":"ltr"}setTabFocus(t){}ngOnChanges(t){const{position:n}=t;n&&!n.isFirstChange()&&(this.alignInkBarToSelectedTab(),this.lockAnimation(),this.updateScrollListPosition())}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.sBO),e.Y36(e.R0b),e.Y36(G.rL),e.Y36(lt.D3),e.Y36(J.Is,8))},i.\u0275cmp=e.Xpm({type:i,selectors:[["nz-tabs-nav"]],contentQueries:function(t,n,s){if(1&t&&e.Suo(s,zt,5),2&t){let o;e.iGM(o=e.CRH())&&(n.items=o)}},viewQuery:function(t,n){if(1&t&&(e.Gf(_t,7),e.Gf(It,7),e.Gf(Tt,7),e.Gf(it,5),e.Gf(gt,7)),2&t){let s;e.iGM(s=e.CRH())&&(n.navWarpRef=s.first),e.iGM(s=e.CRH())&&(n.navListRef=s.first),e.iGM(s=e.CRH())&&(n.operationRef=s.first),e.iGM(s=e.CRH())&&(n.addBtnRef=s.first),e.iGM(s=e.CRH())&&(n.inkBar=s.first)}},hostAttrs:["role","tablist",1,"ant-tabs-nav"],hostBindings:function(t,n){1&t&&e.NdJ("keydown",function(o){return n.handleKeydown(o)})},inputs:{position:"position",addable:"addable",hideBar:"hideBar",addIcon:"addIcon",inkBarAnimated:"inkBarAnimated",extraTemplate:"extraTemplate",selectedIndex:"selectedIndex"},outputs:{indexFocused:"indexFocused",selectFocusedIndex:"selectFocusedIndex",addClicked:"addClicked",tabScroll:"tabScroll"},exportAs:["nzTabsNav"],features:[e.TTD],ngContentSelectors:d,decls:9,vars:16,consts:[[1,"ant-tabs-nav-wrap"],["navWarp",""],["nzTabScrollList","",1,"ant-tabs-nav-list",3,"offsetChange","tabScroll"],["navList",""],["nz-tab-add-button","",3,"addIcon","click",4,"ngIf"],["nz-tabs-ink-bar","",3,"hidden","position","animated"],[3,"addIcon","addable","items","addClicked","selected"],["class","ant-tabs-extra-content",4,"ngIf"],["nz-tab-add-button","",3,"addIcon","click"],[1,"ant-tabs-extra-content"],[3,"ngTemplateOutlet"]],template:function(t,n){1&t&&(e.F$t(),e.TgZ(0,"div",0,1)(2,"div",2,3),e.NdJ("offsetChange",function(o){return n.onOffsetChange(o)})("tabScroll",function(o){return n.tabScroll.emit(o)}),e.Hsn(4),e.YNc(5,xt,1,1,"button",4),e._UZ(6,"div",5),e.qZA()(),e.TgZ(7,"nz-tab-nav-operation",6),e.NdJ("addClicked",function(){return n.addClicked.emit()})("selected",function(o){return n.onSelectedFromMenu(o)}),e.qZA(),e.YNc(8,_,2,1,"div",7)),2&t&&(e.ekj("ant-tabs-nav-wrap-ping-left",n.pingLeft)("ant-tabs-nav-wrap-ping-right",n.pingRight)("ant-tabs-nav-wrap-ping-top",n.pingTop)("ant-tabs-nav-wrap-ping-bottom",n.pingBottom),e.xp6(5),e.Q6J("ngIf",n.showAddButton),e.xp6(1),e.Q6J("hidden",n.hideBar)("position",n.position)("animated",n.inkBarAnimated),e.xp6(1),e.Q6J("addIcon",n.addIcon)("addable",n.addable)("items",n.hiddenItems),e.xp6(1),e.Q6J("ngIf",n.extraTemplate))},dependencies:[it,Tt,At,z.O5,gt,z.tP],encapsulation:2,changeDetection:0}),i})(),Rt=(()=>{class i{constructor(){this.content=null,this.active=!1,this.tabPaneAnimated=!0,this.forceRender=!1}}return i.\u0275fac=function(t){return new(t||i)},i.\u0275cmp=e.Xpm({type:i,selectors:[["","nz-tab-body",""]],hostAttrs:[1,"ant-tabs-tabpane"],hostVars:12,hostBindings:function(t,n){2&t&&(e.uIk("tabindex",n.active?0:-1)("aria-hidden",!n.active),e.Udp("visibility",n.tabPaneAnimated?n.active?null:"hidden":null)("height",n.tabPaneAnimated?n.active?null:0:null)("overflow-y",n.tabPaneAnimated?n.active?null:"none":null)("display",n.tabPaneAnimated||n.active?null:"none"),e.ekj("ant-tabs-tabpane-active",n.active))},inputs:{content:"content",active:"active",tabPaneAnimated:"tabPaneAnimated",forceRender:"forceRender"},exportAs:["nzTabBody"],attrs:p,decls:1,vars:1,consts:[[4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(t,n){1&t&&e.YNc(0,C,2,1,"ng-container",0),2&t&&e.Q6J("ngIf",n.active||n.forceRender)},dependencies:[z.O5,z.tP],encapsulation:2,changeDetection:0}),i})(),Pt=(()=>{class i{constructor(){this.closeIcon="close"}}return i.\u0275fac=function(t){return new(t||i)},i.\u0275cmp=e.Xpm({type:i,selectors:[["nz-tab-close-button"],["button","nz-tab-close-button",""]],hostAttrs:["aria-label","Close tab","type","button",1,"ant-tabs-tab-remove"],inputs:{closeIcon:"closeIcon"},decls:1,vars:1,consts:[[4,"nzStringTemplateOutlet"],["nz-icon","","nzTheme","outline",3,"nzType"]],template:function(t,n){1&t&&e.YNc(0,E,2,1,"ng-container",0),2&t&&e.Q6J("nzStringTemplateOutlet",n.closeIcon)},dependencies:[S.f,Y.Ls],encapsulation:2}),i})(),Bt=(()=>{class i{constructor(t){this.templateRef=t}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.Rgc,1))},i.\u0275dir=e.lG2({type:i,selectors:[["ng-template","nzTabLink",""]],exportAs:["nzTabLinkTemplate"]}),i})(),wt=(()=>{class i{constructor(t,n,s){this.elementRef=t,this.routerLink=n,this.routerLinkWithHref=s}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(e.SBq),e.Y36(V.rH,10),e.Y36(V.yS,10))},i.\u0275dir=e.lG2({type:i,selectors:[["a","nz-tab-link",""]],exportAs:["nzTabLink"]}),i})(),Lt=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275dir=e.lG2({type:i,selectors:[["","nz-tab",""]],exportAs:["nzTab"]}),i})();const kt=new e.OlP("NZ_TAB_SET");let Dt=(()=>{class i{constructor(t){this.closestTabSet=t,this.nzTitle="",this.nzClosable=!1,this.nzCloseIcon="close",this.nzDisabled=!1,this.nzForceRender=!1,this.nzSelect=new e.vpe,this.nzDeselect=new e.vpe,this.nzClick=new e.vpe,this.nzContextmenu=new e.vpe,this.template=null,this.isActive=!1,this.position=null,this.origin=null,this.stateChanges=new K.x}get content(){return this.template||this.contentTemplate}get label(){return this.nzTitle||this.nzTabLinkTemplateDirective?.templateRef}ngOnChanges(t){const{nzTitle:n,nzDisabled:s,nzForceRender:o}=t;(n||s||o)&&this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(kt))},i.\u0275cmp=e.Xpm({type:i,selectors:[["nz-tab"]],contentQueries:function(t,n,s){if(1&t&&(e.Suo(s,Bt,5),e.Suo(s,Lt,5,e.Rgc),e.Suo(s,wt,5)),2&t){let o;e.iGM(o=e.CRH())&&(n.nzTabLinkTemplateDirective=o.first),e.iGM(o=e.CRH())&&(n.template=o.first),e.iGM(o=e.CRH())&&(n.linkDirective=o.first)}},viewQuery:function(t,n){if(1&t&&e.Gf(y,7),2&t){let s;e.iGM(s=e.CRH())&&(n.contentTemplate=s.first)}},inputs:{nzTitle:"nzTitle",nzClosable:"nzClosable",nzCloseIcon:"nzCloseIcon",nzDisabled:"nzDisabled",nzForceRender:"nzForceRender"},outputs:{nzSelect:"nzSelect",nzDeselect:"nzDeselect",nzClick:"nzClick",nzContextmenu:"nzContextmenu"},exportAs:["nzTab"],features:[e.TTD],ngContentSelectors:T,decls:4,vars:0,consts:[["tabLinkTemplate",""],["contentTemplate",""]],template:function(t,n){1&t&&(e.F$t(vt),e.YNc(0,P,1,0,"ng-template",null,0,e.W1O),e.YNc(2,bt,1,0,"ng-template",null,1,e.W1O))},encapsulation:2,changeDetection:0}),(0,b.gn)([(0,D.yF)()],i.prototype,"nzClosable",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzDisabled",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzForceRender",void 0),i})();class Vt{}let Qt=0,Wt=(()=>{class i{constructor(t,n,s,o,h){this.nzConfigService=t,this.ngZone=n,this.cdr=s,this.directionality=o,this.router=h,this._nzModuleName="tabs",this.nzTabPosition="top",this.nzCanDeactivate=null,this.nzAddIcon="plus",this.nzTabBarStyle=null,this.nzType="line",this.nzSize="default",this.nzAnimated=!0,this.nzTabBarGutter=void 0,this.nzHideAdd=!1,this.nzCentered=!1,this.nzHideAll=!1,this.nzLinkRouter=!1,this.nzLinkExact=!0,this.nzSelectChange=new e.vpe(!0),this.nzSelectedIndexChange=new e.vpe,this.nzTabListScroll=new e.vpe,this.nzClose=new e.vpe,this.nzAdd=new e.vpe,this.allTabs=new e.n_E,this.tabs=new e.n_E,this.dir="ltr",this.destroy$=new K.x,this.indexToSelect=0,this.selectedIndex=null,this.tabLabelSubscription=U.w0.EMPTY,this.tabsSubscription=U.w0.EMPTY,this.canDeactivateSubscription=U.w0.EMPTY,this.tabSetId=Qt++}get nzSelectedIndex(){return this.selectedIndex}set nzSelectedIndex(t){this.indexToSelect=(0,$.su)(t,null)}get position(){return-1===["top","bottom"].indexOf(this.nzTabPosition)?"vertical":"horizontal"}get addable(){return"editable-card"===this.nzType&&!this.nzHideAdd}get closable(){return"editable-card"===this.nzType}get line(){return"line"===this.nzType}get inkBarAnimated(){return this.line&&("boolean"==typeof this.nzAnimated?this.nzAnimated:this.nzAnimated.inkBar)}get tabPaneAnimated(){return"horizontal"===this.position&&this.line&&("boolean"==typeof this.nzAnimated?this.nzAnimated:this.nzAnimated.tabPane)}ngOnInit(){this.dir=this.directionality.value,this.directionality.change?.pipe((0,N.R)(this.destroy$)).subscribe(t=>{this.dir=t,this.cdr.detectChanges()})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.tabs.destroy(),this.tabLabelSubscription.unsubscribe(),this.tabsSubscription.unsubscribe(),this.canDeactivateSubscription.unsubscribe()}ngAfterContentInit(){this.ngZone.runOutsideAngular(()=>{Promise.resolve().then(()=>this.setUpRouter())}),this.subscribeToTabLabels(),this.subscribeToAllTabChanges(),this.tabsSubscription=this.tabs.changes.subscribe(()=>{if(this.clampTabIndex(this.indexToSelect)===this.selectedIndex){const n=this.tabs.toArray();for(let s=0;s<n.length;s++)if(n[s].isActive){this.indexToSelect=this.selectedIndex=s;break}}this.subscribeToTabLabels(),this.cdr.markForCheck()})}ngAfterContentChecked(){const t=this.indexToSelect=this.clampTabIndex(this.indexToSelect);if(this.selectedIndex!==t){const n=null==this.selectedIndex;n||this.nzSelectChange.emit(this.createChangeEvent(t)),Promise.resolve().then(()=>{this.tabs.forEach((s,o)=>s.isActive=o===t),n||this.nzSelectedIndexChange.emit(t)})}this.tabs.forEach((n,s)=>{n.position=s-t,null!=this.selectedIndex&&0===n.position&&!n.origin&&(n.origin=t-this.selectedIndex)}),this.selectedIndex!==t&&(this.selectedIndex=t,this.cdr.markForCheck())}onClose(t,n){n.preventDefault(),n.stopPropagation(),this.nzClose.emit({index:t})}onAdd(){this.nzAdd.emit()}clampTabIndex(t){return Math.min(this.tabs.length-1,Math.max(t||0,0))}createChangeEvent(t){const n=new Vt;return n.index=t,this.tabs&&this.tabs.length&&(n.tab=this.tabs.toArray()[t],this.tabs.forEach((s,o)=>{o!==t&&s.nzDeselect.emit()}),n.tab.nzSelect.emit()),n}subscribeToTabLabels(){this.tabLabelSubscription&&this.tabLabelSubscription.unsubscribe(),this.tabLabelSubscription=(0,x.T)(...this.tabs.map(t=>t.stateChanges)).subscribe(()=>this.cdr.markForCheck())}subscribeToAllTabChanges(){this.allTabs.changes.pipe((0,L.O)(this.allTabs)).subscribe(t=>{this.tabs.reset(t.filter(n=>n.closestTabSet===this)),this.tabs.notifyOnChanges()})}canDeactivateFun(t,n){return"function"==typeof this.nzCanDeactivate?(0,D.lN)(this.nzCanDeactivate(t,n)).pipe((0,tt.P)(),(0,N.R)(this.destroy$)):(0,q.of)(!0)}clickNavItem(t,n,s){t.nzDisabled||(t.nzClick.emit(),this.isRouterLinkClickEvent(n,s)||this.setSelectedIndex(n))}isRouterLinkClickEvent(t,n){const s=n.target;return!!this.nzLinkRouter&&!!this.tabs.toArray()[t]?.linkDirective?.elementRef.nativeElement.contains(s)}contextmenuNavItem(t,n){t.nzDisabled||t.nzContextmenu.emit(n)}setSelectedIndex(t){this.canDeactivateSubscription.unsubscribe(),this.canDeactivateSubscription=this.canDeactivateFun(this.selectedIndex,t).subscribe(n=>{n&&(this.nzSelectedIndex=t,this.tabNavBarRef.focusIndex=t,this.cdr.markForCheck())})}getTabIndex(t,n){return t.nzDisabled?null:this.selectedIndex===n?0:-1}getTabContentId(t){return`nz-tabs-${this.tabSetId}-tab-${t}`}setUpRouter(){if(this.nzLinkRouter){if(!this.router)throw new Error(`${ct.Bq} you should import 'RouterModule' if you want to use 'nzLinkRouter'!`);this.router.events.pipe((0,N.R)(this.destroy$),(0,R.h)(t=>t instanceof V.m2),(0,L.O)(!0),(0,Z.g)(0)).subscribe(()=>{this.updateRouterActive(),this.cdr.markForCheck()})}}updateRouterActive(){if(this.router.navigated){const t=this.findShouldActiveTabIndex();t!==this.selectedIndex&&this.setSelectedIndex(t),this.nzHideAll=-1===t}}findShouldActiveTabIndex(){const t=this.tabs.toArray(),n=this.isLinkActive(this.router);return t.findIndex(s=>{const o=s.linkDirective;return!!o&&(n(o.routerLink)||n(o.routerLinkWithHref))})}isLinkActive(t){return n=>!!n&&t.isActive(n.urlTree||"",{paths:this.nzLinkExact?"exact":"subset",queryParams:this.nzLinkExact?"exact":"subset",fragment:"ignored",matrixParams:"ignored"})}getTabContentMarginValue(){return 100*-(this.nzSelectedIndex||0)}getTabContentMarginLeft(){return this.tabPaneAnimated&&"rtl"!==this.dir?`${this.getTabContentMarginValue()}%`:""}getTabContentMarginRight(){return this.tabPaneAnimated&&"rtl"===this.dir?`${this.getTabContentMarginValue()}%`:""}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(W.jY),e.Y36(e.R0b),e.Y36(e.sBO),e.Y36(J.Is,8),e.Y36(V.F0,8))},i.\u0275cmp=e.Xpm({type:i,selectors:[["nz-tabset"]],contentQueries:function(t,n,s){if(1&t&&e.Suo(s,Dt,5),2&t){let o;e.iGM(o=e.CRH())&&(n.allTabs=o)}},viewQuery:function(t,n){if(1&t&&e.Gf(Ct,5),2&t){let s;e.iGM(s=e.CRH())&&(n.tabNavBarRef=s.first)}},hostAttrs:[1,"ant-tabs"],hostVars:24,hostBindings:function(t,n){2&t&&e.ekj("ant-tabs-card","card"===n.nzType||"editable-card"===n.nzType)("ant-tabs-editable","editable-card"===n.nzType)("ant-tabs-editable-card","editable-card"===n.nzType)("ant-tabs-centered",n.nzCentered)("ant-tabs-rtl","rtl"===n.dir)("ant-tabs-top","top"===n.nzTabPosition)("ant-tabs-bottom","bottom"===n.nzTabPosition)("ant-tabs-left","left"===n.nzTabPosition)("ant-tabs-right","right"===n.nzTabPosition)("ant-tabs-default","default"===n.nzSize)("ant-tabs-small","small"===n.nzSize)("ant-tabs-large","large"===n.nzSize)},inputs:{nzSelectedIndex:"nzSelectedIndex",nzTabPosition:"nzTabPosition",nzTabBarExtraContent:"nzTabBarExtraContent",nzCanDeactivate:"nzCanDeactivate",nzAddIcon:"nzAddIcon",nzTabBarStyle:"nzTabBarStyle",nzType:"nzType",nzSize:"nzSize",nzAnimated:"nzAnimated",nzTabBarGutter:"nzTabBarGutter",nzHideAdd:"nzHideAdd",nzCentered:"nzCentered",nzHideAll:"nzHideAll",nzLinkRouter:"nzLinkRouter",nzLinkExact:"nzLinkExact"},outputs:{nzSelectChange:"nzSelectChange",nzSelectedIndexChange:"nzSelectedIndexChange",nzTabListScroll:"nzTabListScroll",nzClose:"nzClose",nzAdd:"nzAdd"},exportAs:["nzTabset"],features:[e._Bn([{provide:kt,useExisting:i}])],decls:4,vars:16,consts:[[3,"ngStyle","selectedIndex","inkBarAnimated","addable","addIcon","hideBar","position","extraTemplate","tabScroll","selectFocusedIndex","addClicked",4,"ngIf"],[1,"ant-tabs-content-holder"],[1,"ant-tabs-content"],["nz-tab-body","",3,"active","content","forceRender","tabPaneAnimated",4,"ngFor","ngForOf"],[3,"ngStyle","selectedIndex","inkBarAnimated","addable","addIcon","hideBar","position","extraTemplate","tabScroll","selectFocusedIndex","addClicked"],["class","ant-tabs-tab",3,"margin-right","margin-bottom","ant-tabs-tab-active","ant-tabs-tab-disabled","click","contextmenu",4,"ngFor","ngForOf"],[1,"ant-tabs-tab",3,"click","contextmenu"],["role","tab","nzTabNavItem","","cdkMonitorElementFocus","",1,"ant-tabs-tab-btn",3,"disabled","tab","active"],[4,"nzStringTemplateOutlet","nzStringTemplateOutletContext"],["nz-tab-close-button","",3,"closeIcon","click",4,"ngIf"],["nz-tab-close-button","",3,"closeIcon","click"],["nz-tab-body","",3,"active","content","forceRender","tabPaneAnimated"]],template:function(t,n){1&t&&(e.YNc(0,Ut,2,9,"nz-tabs-nav",0),e.TgZ(1,"div",1)(2,"div",2),e.YNc(3,Kt,1,4,"div",3),e.qZA()()),2&t&&(e.Q6J("ngIf",n.tabs.length||n.addable),e.xp6(2),e.Udp("margin-left",n.getTabContentMarginLeft())("margin-right",n.getTabContentMarginRight()),e.ekj("ant-tabs-content-top","top"===n.nzTabPosition)("ant-tabs-content-bottom","bottom"===n.nzTabPosition)("ant-tabs-content-left","left"===n.nzTabPosition)("ant-tabs-content-right","right"===n.nzTabPosition)("ant-tabs-content-animated",n.tabPaneAnimated),e.xp6(1),e.Q6J("ngForOf",n.tabs))},dependencies:[Ct,Pt,Rt,z.O5,z.PC,z.sg,zt,w.kH,S.f],encapsulation:2}),(0,b.gn)([(0,W.oS)()],i.prototype,"nzType",void 0),(0,b.gn)([(0,W.oS)()],i.prototype,"nzSize",void 0),(0,b.gn)([(0,W.oS)()],i.prototype,"nzAnimated",void 0),(0,b.gn)([(0,W.oS)()],i.prototype,"nzTabBarGutter",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzHideAdd",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzCentered",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzHideAll",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzLinkRouter",void 0),(0,b.gn)([(0,D.yF)()],i.prototype,"nzLinkExact",void 0),i})(),jt=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.oAB({type:i}),i.\u0275inj=e.cJS({imports:[[J.vT,z.ez,k.Q8,Y.PV,S.T,dt.ud,w.rt,G.ZD,M.b1]]}),i})()}}]);