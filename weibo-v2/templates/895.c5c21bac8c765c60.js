"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[895],{895:(_e,j,R)=>{R.r(j),R.d(j,{DocPageModule:()=>$e});var K=R(5708),ee=R(4284);let D={baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};const ne=/[&<>"']/,ie=/[&<>"']/g,se=/[<>"']|&(?!#?\w+;)/,re=/[<>"']|&(?!#?\w+;)/g,le={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Q=r=>le[r];function x(r,e){if(e){if(ne.test(r))return r.replace(ie,Q)}else if(se.test(r))return r.replace(re,Q);return r}const ae=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function N(r){return r.replace(ae,(e,n)=>"colon"===(n=n.toLowerCase())?":":"#"===n.charAt(0)?"x"===n.charAt(1)?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):"")}const oe=/(^|[^\[])\^/g;function d(r,e){r="string"==typeof r?r:r.source,e=e||"";const n={replace:(t,i)=>(i=(i=i.source||i).replace(oe,"$1"),r=r.replace(t,i),n),getRegex:()=>new RegExp(r,e)};return n}const ce=/[^\w:]/g,he=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function F(r,e,n){if(r){let t;try{t=decodeURIComponent(N(n)).replace(ce,"").toLowerCase()}catch{return null}if(0===t.indexOf("javascript:")||0===t.indexOf("vbscript:")||0===t.indexOf("data:"))return null}e&&!he.test(n)&&(n=function fe(r,e){v[" "+r]||(v[" "+r]=pe.test(r)?r+"/":M(r,"/",!0));const n=-1===(r=v[" "+r]).indexOf(":");return"//"===e.substring(0,2)?n?e:r.replace(ue,"$1")+e:"/"===e.charAt(0)?n?e:r.replace(ge,"$1")+e:r+e}(e,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const v={},pe=/^[^:]+:\/*[^/]*$/,ue=/^([^:]+:)[\s\S]*$/,ge=/^([^:]+:\/*[^/]*)[\s\S]*$/,O={exec:function(){}};function y(r){let n,t,e=1;for(;e<arguments.length;e++)for(t in n=arguments[e],n)Object.prototype.hasOwnProperty.call(n,t)&&(r[t]=n[t]);return r}function H(r,e){const t=r.replace(/\|/g,(s,l,a)=>{let c=!1,g=l;for(;--g>=0&&"\\"===a[g];)c=!c;return c?"|":" |"}).split(/ \|/);let i=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;i<t.length;i++)t[i]=t[i].trim().replace(/\\\|/g,"|");return t}function M(r,e,n){const t=r.length;if(0===t)return"";let i=0;for(;i<t;){const s=r.charAt(t-i-1);if(s!==e||n){if(s===e||!n)break;i++}else i++}return r.slice(0,t-i)}function J(r){r&&r.sanitize&&!r.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function X(r,e){if(e<1)return"";let n="";for(;e>1;)1&e&&(n+=r),e>>=1,r+=r;return n+r}function W(r,e,n,t){const i=e.href,s=e.title?x(e.title):null,l=r[1].replace(/\\([\[\]])/g,"$1");if("!"!==r[0].charAt(0)){t.state.inLink=!0;const a={type:"link",raw:n,href:i,title:s,text:l,tokens:t.inlineTokens(l,[])};return t.state.inLink=!1,a}return{type:"image",raw:n,href:i,title:s,text:x(l)}}class P{constructor(e){this.options=e||D}space(e){const n=this.rules.block.newline.exec(e);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(e){const n=this.rules.block.code.exec(e);if(n){const t=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?t:M(t,"\n")}}}fences(e){const n=this.rules.block.fences.exec(e);if(n){const t=n[0],i=function ke(r,e){const n=r.match(/^(\s+)(?:```)/);if(null===n)return e;const t=n[1];return e.split("\n").map(i=>{const s=i.match(/^\s+/);if(null===s)return i;const[l]=s;return l.length>=t.length?i.slice(t.length):i}).join("\n")}(t,n[3]||"");return{type:"code",raw:t,lang:n[2]?n[2].trim():n[2],text:i}}}heading(e){const n=this.rules.block.heading.exec(e);if(n){let t=n[2].trim();if(/#$/.test(t)){const s=M(t,"#");(this.options.pedantic||!s||/ $/.test(s))&&(t=s.trim())}const i={type:"heading",raw:n[0],depth:n[1].length,text:t,tokens:[]};return this.lexer.inline(i.text,i.tokens),i}}hr(e){const n=this.rules.block.hr.exec(e);if(n)return{type:"hr",raw:n[0]}}blockquote(e){const n=this.rules.block.blockquote.exec(e);if(n){const t=n[0].replace(/^ *>[ \t]?/gm,"");return{type:"blockquote",raw:n[0],tokens:this.lexer.blockTokens(t,[]),text:t}}}list(e){let n=this.rules.block.list.exec(e);if(n){let t,i,s,l,a,c,g,f,b,k,p,E,$=n[1].trim();const A=$.length>1,m={type:"list",raw:"",ordered:A,start:A?+$.slice(0,-1):"",loose:!1,items:[]};$=A?`\\d{1,9}\\${$.slice(-1)}`:`\\${$}`,this.options.pedantic&&($=A?$:"[*+-]");const w=new RegExp(`^( {0,3}${$})((?:[\t ][^\\n]*)?(?:\\n|$))`);for(;e&&(E=!1,(n=w.exec(e))&&!this.rules.block.hr.test(e));){if(t=n[0],e=e.substring(t.length),f=n[2].split("\n",1)[0],b=e.split("\n",1)[0],this.options.pedantic?(l=2,p=f.trimLeft()):(l=n[2].search(/[^ ]/),l=l>4?1:l,p=f.slice(l),l+=n[1].length),c=!1,!f&&/^ *$/.test(b)&&(t+=b+"\n",e=e.substring(b.length+1),E=!0),!E){const C=new RegExp(`^ {0,${Math.min(3,l-1)}}(?:[*+-]|\\d{1,9}[.)])((?: [^\\n]*)?(?:\\n|$))`),S=new RegExp(`^ {0,${Math.min(3,l-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),T=new RegExp(`^ {0,${Math.min(3,l-1)}}(?:\`\`\`|~~~)`),L=new RegExp(`^ {0,${Math.min(3,l-1)}}#`);for(;e&&(k=e.split("\n",1)[0],f=k,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(T.test(f)||L.test(f)||C.test(f)||S.test(e)));){if(f.search(/[^ ]/)>=l||!f.trim())p+="\n"+f.slice(l);else{if(c)break;p+="\n"+f}!c&&!f.trim()&&(c=!0),t+=k+"\n",e=e.substring(k.length+1)}}m.loose||(g?m.loose=!0:/\n *\n *$/.test(t)&&(g=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(p),i&&(s="[ ] "!==i[0],p=p.replace(/^\[[ xX]\] +/,""))),m.items.push({type:"list_item",raw:t,task:!!i,checked:s,loose:!1,text:p}),m.raw+=t}m.items[m.items.length-1].raw=t.trimRight(),m.items[m.items.length-1].text=p.trimRight(),m.raw=m.raw.trimRight();const B=m.items.length;for(a=0;a<B;a++){this.lexer.state.top=!1,m.items[a].tokens=this.lexer.blockTokens(m.items[a].text,[]);const C=m.items[a].tokens.filter(T=>"space"===T.type),S=C.every(T=>{const L=T.raw.split("");let Z=0;for(const ze of L)if("\n"===ze&&(Z+=1),Z>1)return!0;return!1});!m.loose&&C.length&&S&&(m.loose=!0,m.items[a].loose=!0)}return m}}html(e){const n=this.rules.block.html.exec(e);if(n){const t={type:"html",raw:n[0],pre:!this.options.sanitizer&&("pre"===n[1]||"script"===n[1]||"style"===n[1]),text:n[0]};return this.options.sanitize&&(t.type="paragraph",t.text=this.options.sanitizer?this.options.sanitizer(n[0]):x(n[0]),t.tokens=[],this.lexer.inline(t.text,t.tokens)),t}}def(e){const n=this.rules.block.def.exec(e);if(n)return n[3]&&(n[3]=n[3].substring(1,n[3].length-1)),{type:"def",tag:n[1].toLowerCase().replace(/\s+/g," "),raw:n[0],href:n[2],title:n[3]}}table(e){const n=this.rules.block.table.exec(e);if(n){const t={type:"table",header:H(n[1]).map(i=>({text:i})),align:n[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(t.header.length===t.align.length){t.raw=n[0];let s,l,a,c,i=t.align.length;for(s=0;s<i;s++)t.align[s]=/^ *-+: *$/.test(t.align[s])?"right":/^ *:-+: *$/.test(t.align[s])?"center":/^ *:-+ *$/.test(t.align[s])?"left":null;for(i=t.rows.length,s=0;s<i;s++)t.rows[s]=H(t.rows[s],t.header.length).map(g=>({text:g}));for(i=t.header.length,l=0;l<i;l++)t.header[l].tokens=[],this.lexer.inline(t.header[l].text,t.header[l].tokens);for(i=t.rows.length,l=0;l<i;l++)for(c=t.rows[l],a=0;a<c.length;a++)c[a].tokens=[],this.lexer.inline(c[a].text,c[a].tokens);return t}}}lheading(e){const n=this.rules.block.lheading.exec(e);if(n){const t={type:"heading",raw:n[0],depth:"="===n[2].charAt(0)?1:2,text:n[1],tokens:[]};return this.lexer.inline(t.text,t.tokens),t}}paragraph(e){const n=this.rules.block.paragraph.exec(e);if(n){const t={type:"paragraph",raw:n[0],text:"\n"===n[1].charAt(n[1].length-1)?n[1].slice(0,-1):n[1],tokens:[]};return this.lexer.inline(t.text,t.tokens),t}}text(e){const n=this.rules.block.text.exec(e);if(n){const t={type:"text",raw:n[0],text:n[0],tokens:[]};return this.lexer.inline(t.text,t.tokens),t}}escape(e){const n=this.rules.inline.escape.exec(e);if(n)return{type:"escape",raw:n[0],text:x(n[1])}}tag(e){const n=this.rules.inline.tag.exec(e);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):x(n[0]):n[0]}}link(e){const n=this.rules.inline.link.exec(e);if(n){const t=n[2].trim();if(!this.options.pedantic&&/^</.test(t)){if(!/>$/.test(t))return;const l=M(t.slice(0,-1),"\\");if((t.length-l.length)%2==0)return}else{const l=function de(r,e){if(-1===r.indexOf(e[1]))return-1;const n=r.length;let t=0,i=0;for(;i<n;i++)if("\\"===r[i])i++;else if(r[i]===e[0])t++;else if(r[i]===e[1]&&(t--,t<0))return i;return-1}(n[2],"()");if(l>-1){const c=(0===n[0].indexOf("!")?5:4)+n[1].length+l;n[2]=n[2].substring(0,l),n[0]=n[0].substring(0,c).trim(),n[3]=""}}let i=n[2],s="";if(this.options.pedantic){const l=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);l&&(i=l[1],s=l[3])}else s=n[3]?n[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(i=this.options.pedantic&&!/>$/.test(t)?i.slice(1):i.slice(1,-1)),W(n,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:s&&s.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(e,n){let t;if((t=this.rules.inline.reflink.exec(e))||(t=this.rules.inline.nolink.exec(e))){let i=(t[2]||t[1]).replace(/\s+/g," ");if(i=n[i.toLowerCase()],!i||!i.href){const s=t[0].charAt(0);return{type:"text",raw:s,text:s}}return W(t,i,t[0],this.lexer)}}emStrong(e,n,t=""){let i=this.rules.inline.emStrong.lDelim.exec(e);if(!i||i[3]&&t.match(/[\p{L}\p{N}]/u))return;const s=i[1]||i[2]||"";if(!s||s&&(""===t||this.rules.inline.punctuation.exec(t))){const l=i[0].length-1;let a,c,g=l,f=0;const b="*"===i[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(b.lastIndex=0,n=n.slice(-1*e.length+l);null!=(i=b.exec(n));){if(a=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!a)continue;if(c=a.length,i[3]||i[4]){g+=c;continue}if((i[5]||i[6])&&l%3&&!((l+c)%3)){f+=c;continue}if(g-=c,g>0)continue;if(c=Math.min(c,c+g+f),Math.min(l,c)%2){const p=e.slice(1,l+i.index+c);return{type:"em",raw:e.slice(0,l+i.index+c+1),text:p,tokens:this.lexer.inlineTokens(p,[])}}const k=e.slice(2,l+i.index+c-1);return{type:"strong",raw:e.slice(0,l+i.index+c+1),text:k,tokens:this.lexer.inlineTokens(k,[])}}}}codespan(e){const n=this.rules.inline.code.exec(e);if(n){let t=n[2].replace(/\n/g," ");const i=/[^ ]/.test(t),s=/^ /.test(t)&&/ $/.test(t);return i&&s&&(t=t.substring(1,t.length-1)),t=x(t,!0),{type:"codespan",raw:n[0],text:t}}}br(e){const n=this.rules.inline.br.exec(e);if(n)return{type:"br",raw:n[0]}}del(e){const n=this.rules.inline.del.exec(e);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2],[])}}autolink(e,n){const t=this.rules.inline.autolink.exec(e);if(t){let i,s;return"@"===t[2]?(i=x(this.options.mangle?n(t[1]):t[1]),s="mailto:"+i):(i=x(t[1]),s=i),{type:"link",raw:t[0],text:i,href:s,tokens:[{type:"text",raw:i,text:i}]}}}url(e,n){let t;if(t=this.rules.inline.url.exec(e)){let i,s;if("@"===t[2])i=x(this.options.mangle?n(t[0]):t[0]),s="mailto:"+i;else{let l;do{l=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])[0]}while(l!==t[0]);i=x(t[0]),s="www."===t[1]?"http://"+i:i}return{type:"link",raw:t[0],text:i,href:s,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(e,n){const t=this.rules.inline.text.exec(e);if(t){let i;return i=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):x(t[0]):t[0]:x(this.options.smartypants?n(t[0]):t[0]),{type:"text",raw:t[0],text:i}}}}const h={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?<?([^\s>]+)>?(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:O,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};h.def=d(h.def).replace("label",h._label).replace("title",h._title).getRegex(),h.bullet=/(?:[*+-]|\d{1,9}[.)])/,h.listItemStart=d(/^( *)(bull) */).replace("bull",h.bullet).getRegex(),h.list=d(h.list).replace(/bull/g,h.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+h.def.source+")").getRegex(),h._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",h._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,h.html=d(h.html,"i").replace("comment",h._comment).replace("tag",h._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),h.paragraph=d(h._paragraph).replace("hr",h.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",h._tag).getRegex(),h.blockquote=d(h.blockquote).replace("paragraph",h.paragraph).getRegex(),h.normal=y({},h),h.gfm=y({},h.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),h.gfm.table=d(h.gfm.table).replace("hr",h.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",h._tag).getRegex(),h.gfm.paragraph=d(h._paragraph).replace("hr",h.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",h.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",h._tag).getRegex(),h.pedantic=y({},h.normal,{html:d("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",h._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:O,paragraph:d(h.normal._paragraph).replace("hr",h.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",h.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});const o={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:O,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^[^_*]*?\_\_[^_*]*?\*[^_*]*?(?=\_\_)|[^*]+(?=[^*])|[punct_](\*+)(?=[\s]|$)|[^punct*_\s](\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|[^punct*_\s](\*+)(?=[^punct*_\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?\_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|[punct*](\_+)(?=[\s]|$)|[^punct*_\s](\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:O,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function me(r){return r.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1\u201c").replace(/"/g,"\u201d").replace(/\.{3}/g,"\u2026")}function G(r){let n,t,e="";const i=r.length;for(n=0;n<i;n++)t=r.charCodeAt(n),Math.random()>.5&&(t="x"+t.toString(16)),e+="&#"+t+";";return e}o._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",o.punctuation=d(o.punctuation).replace(/punctuation/g,o._punctuation).getRegex(),o.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,o.escapedEmSt=/\\\*|\\_/g,o._comment=d(h._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),o.emStrong.lDelim=d(o.emStrong.lDelim).replace(/punct/g,o._punctuation).getRegex(),o.emStrong.rDelimAst=d(o.emStrong.rDelimAst,"g").replace(/punct/g,o._punctuation).getRegex(),o.emStrong.rDelimUnd=d(o.emStrong.rDelimUnd,"g").replace(/punct/g,o._punctuation).getRegex(),o._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,o._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,o._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,o.autolink=d(o.autolink).replace("scheme",o._scheme).replace("email",o._email).getRegex(),o._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,o.tag=d(o.tag).replace("comment",o._comment).replace("attribute",o._attribute).getRegex(),o._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,o._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,o._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,o.link=d(o.link).replace("label",o._label).replace("href",o._href).replace("title",o._title).getRegex(),o.reflink=d(o.reflink).replace("label",o._label).replace("ref",h._label).getRegex(),o.nolink=d(o.nolink).replace("ref",h._label).getRegex(),o.reflinkSearch=d(o.reflinkSearch,"g").replace("reflink",o.reflink).replace("nolink",o.nolink).getRegex(),o.normal=y({},o),o.pedantic=y({},o.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:d(/^!?\[(label)\]\((.*?)\)/).replace("label",o._label).getRegex(),reflink:d(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",o._label).getRegex()}),o.gfm=y({},o.normal,{escape:d(o.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),o.gfm.url=d(o.gfm.url,"i").replace("email",o.gfm._extended_email).getRegex(),o.breaks=y({},o.gfm,{br:d(o.br).replace("{2,}","*").getRegex(),text:d(o.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});class z{constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||D,this.options.tokenizer=this.options.tokenizer||new P,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:h.normal,inline:o.normal};this.options.pedantic?(n.block=h.pedantic,n.inline=o.pedantic):this.options.gfm&&(n.block=h.gfm,n.inline=this.options.breaks?o.breaks:o.gfm),this.tokenizer.rules=n}static get rules(){return{block:h,inline:o}}static lex(e,n){return new z(n).lex(e)}static lexInline(e,n){return new z(n).inlineTokens(e)}lex(e){let n;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(e,n=[]){let t,i,s,l;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(a,c,g)=>c+"    ".repeat(g.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>!!(t=a.call({lexer:this},e,n))&&(e=e.substring(t.raw.length),n.push(t),!0)))){if(t=this.tokenizer.space(e)){e=e.substring(t.raw.length),1===t.raw.length&&n.length>0?n[n.length-1].raw+="\n":n.push(t);continue}if(t=this.tokenizer.code(e)){e=e.substring(t.raw.length),i=n[n.length-1],!i||"paragraph"!==i.type&&"text"!==i.type?n.push(t):(i.raw+="\n"+t.raw,i.text+="\n"+t.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text);continue}if(t=this.tokenizer.fences(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.heading(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.hr(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.blockquote(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.list(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.html(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.def(e)){e=e.substring(t.raw.length),i=n[n.length-1],!i||"paragraph"!==i.type&&"text"!==i.type?this.tokens.links[t.tag]||(this.tokens.links[t.tag]={href:t.href,title:t.title}):(i.raw+="\n"+t.raw,i.text+="\n"+t.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text);continue}if(t=this.tokenizer.table(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.lheading(e)){e=e.substring(t.raw.length),n.push(t);continue}if(s=e,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const c=e.slice(1);let g;this.options.extensions.startBlock.forEach(function(f){g=f.call({lexer:this},c),"number"==typeof g&&g>=0&&(a=Math.min(a,g))}),a<1/0&&a>=0&&(s=e.substring(0,a+1))}if(this.state.top&&(t=this.tokenizer.paragraph(s))){i=n[n.length-1],l&&"paragraph"===i.type?(i.raw+="\n"+t.raw,i.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(t),l=s.length!==e.length,e=e.substring(t.raw.length);continue}if(t=this.tokenizer.text(e)){e=e.substring(t.raw.length),i=n[n.length-1],i&&"text"===i.type?(i.raw+="\n"+t.raw,i.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(t);continue}if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}return this.state.top=!0,n}inline(e,n){this.inlineQueue.push({src:e,tokens:n})}inlineTokens(e,n=[]){let t,i,s,a,c,g,l=e;if(this.tokens.links){const f=Object.keys(this.tokens.links);if(f.length>0)for(;null!=(a=this.tokenizer.rules.inline.reflinkSearch.exec(l));)f.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,a.index)+"["+X("a",a[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(a=this.tokenizer.rules.inline.blockSkip.exec(l));)l=l.slice(0,a.index)+"["+X("a",a[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(a=this.tokenizer.rules.inline.escapedEmSt.exec(l));)l=l.slice(0,a.index)+"++"+l.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex);for(;e;)if(c||(g=""),c=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(f=>!!(t=f.call({lexer:this},e,n))&&(e=e.substring(t.raw.length),n.push(t),!0)))){if(t=this.tokenizer.escape(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.tag(e)){e=e.substring(t.raw.length),i=n[n.length-1],i&&"text"===t.type&&"text"===i.type?(i.raw+=t.raw,i.text+=t.text):n.push(t);continue}if(t=this.tokenizer.link(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(t.raw.length),i=n[n.length-1],i&&"text"===t.type&&"text"===i.type?(i.raw+=t.raw,i.text+=t.text):n.push(t);continue}if(t=this.tokenizer.emStrong(e,l,g)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.codespan(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.br(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.del(e)){e=e.substring(t.raw.length),n.push(t);continue}if(t=this.tokenizer.autolink(e,G)){e=e.substring(t.raw.length),n.push(t);continue}if(!this.state.inLink&&(t=this.tokenizer.url(e,G))){e=e.substring(t.raw.length),n.push(t);continue}if(s=e,this.options.extensions&&this.options.extensions.startInline){let f=1/0;const b=e.slice(1);let k;this.options.extensions.startInline.forEach(function(p){k=p.call({lexer:this},b),"number"==typeof k&&k>=0&&(f=Math.min(f,k))}),f<1/0&&f>=0&&(s=e.substring(0,f+1))}if(t=this.tokenizer.inlineText(s,me)){e=e.substring(t.raw.length),"_"!==t.raw.slice(-1)&&(g=t.raw.slice(-1)),c=!0,i=n[n.length-1],i&&"text"===i.type?(i.raw+=t.raw,i.text+=t.text):n.push(t);continue}if(e){const f="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(f);break}throw new Error(f)}}return n}}class U{constructor(e){this.options=e||D}code(e,n,t){const i=(n||"").match(/\S*/)[0];if(this.options.highlight){const s=this.options.highlight(e,i);null!=s&&s!==e&&(t=!0,e=s)}return e=e.replace(/\n$/,"")+"\n",i?'<pre><code class="'+this.options.langPrefix+x(i,!0)+'">'+(t?e:x(e,!0))+"</code></pre>\n":"<pre><code>"+(t?e:x(e,!0))+"</code></pre>\n"}blockquote(e){return`<blockquote>\n${e}</blockquote>\n`}html(e){return e}heading(e,n,t,i){return this.options.headerIds?`<h${n} id="${this.options.headerPrefix+i.slug(t)}">${e}</h${n}>\n`:`<h${n}>${e}</h${n}>\n`}hr(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}list(e,n,t){const i=n?"ol":"ul";return"<"+i+(n&&1!==t?' start="'+t+'"':"")+">\n"+e+"</"+i+">\n"}listitem(e){return`<li>${e}</li>\n`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return`<p>${e}</p>\n`}table(e,n){return n&&(n=`<tbody>${n}</tbody>`),"<table>\n<thead>\n"+e+"</thead>\n"+n+"</table>\n"}tablerow(e){return`<tr>\n${e}</tr>\n`}tablecell(e,n){const t=n.header?"th":"td";return(n.align?`<${t} align="${n.align}">`:`<${t}>`)+e+`</${t}>\n`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return`<del>${e}</del>`}link(e,n,t){if(null===(e=F(this.options.sanitize,this.options.baseUrl,e)))return t;let i='<a href="'+x(e)+'"';return n&&(i+=' title="'+n+'"'),i+=">"+t+"</a>",i}image(e,n,t){if(null===(e=F(this.options.sanitize,this.options.baseUrl,e)))return t;let i=`<img src="${e}" alt="${t}"`;return n&&(i+=` title="${n}"`),i+=this.options.xhtml?"/>":">",i}text(e){return e}}class V{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,n,t){return""+t}image(e,n,t){return""+t}br(){return""}}class Y{constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,n){let t=e,i=0;if(this.seen.hasOwnProperty(t)){i=this.seen[e];do{i++,t=e+"-"+i}while(this.seen.hasOwnProperty(t))}return n||(this.seen[e]=i,this.seen[t]=0),t}slug(e,n={}){const t=this.serialize(e);return this.getNextSafeSlug(t,n.dryrun)}}class _{constructor(e){this.options=e||D,this.options.renderer=this.options.renderer||new U,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new V,this.slugger=new Y}static parse(e,n){return new _(n).parse(e)}static parseInline(e,n){return new _(n).parseInline(e)}parse(e,n=!0){let i,s,l,a,c,g,f,b,k,p,E,$,A,m,w,B,C,S,T,t="";const L=e.length;for(i=0;i<L;i++)if(p=e[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[p.type]&&(T=this.options.extensions.renderers[p.type].call({parser:this},p),!1!==T||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(p.type)))t+=T||"";else switch(p.type){case"space":continue;case"hr":t+=this.renderer.hr();continue;case"heading":t+=this.renderer.heading(this.parseInline(p.tokens),p.depth,N(this.parseInline(p.tokens,this.textRenderer)),this.slugger);continue;case"code":t+=this.renderer.code(p.text,p.lang,p.escaped);continue;case"table":for(b="",f="",a=p.header.length,s=0;s<a;s++)f+=this.renderer.tablecell(this.parseInline(p.header[s].tokens),{header:!0,align:p.align[s]});for(b+=this.renderer.tablerow(f),k="",a=p.rows.length,s=0;s<a;s++){for(g=p.rows[s],f="",c=g.length,l=0;l<c;l++)f+=this.renderer.tablecell(this.parseInline(g[l].tokens),{header:!1,align:p.align[l]});k+=this.renderer.tablerow(f)}t+=this.renderer.table(b,k);continue;case"blockquote":k=this.parse(p.tokens),t+=this.renderer.blockquote(k);continue;case"list":for(E=p.ordered,$=p.start,A=p.loose,a=p.items.length,k="",s=0;s<a;s++)w=p.items[s],B=w.checked,C=w.task,m="",w.task&&(S=this.renderer.checkbox(B),A?w.tokens.length>0&&"paragraph"===w.tokens[0].type?(w.tokens[0].text=S+" "+w.tokens[0].text,w.tokens[0].tokens&&w.tokens[0].tokens.length>0&&"text"===w.tokens[0].tokens[0].type&&(w.tokens[0].tokens[0].text=S+" "+w.tokens[0].tokens[0].text)):w.tokens.unshift({type:"text",text:S}):m+=S),m+=this.parse(w.tokens,A),k+=this.renderer.listitem(m,C,B);t+=this.renderer.list(k,E,$);continue;case"html":t+=this.renderer.html(p.text);continue;case"paragraph":t+=this.renderer.paragraph(this.parseInline(p.tokens));continue;case"text":for(k=p.tokens?this.parseInline(p.tokens):p.text;i+1<L&&"text"===e[i+1].type;)p=e[++i],k+="\n"+(p.tokens?this.parseInline(p.tokens):p.text);t+=n?this.renderer.paragraph(k):k;continue;default:{const Z='Token with "'+p.type+'" type was not found.';if(this.options.silent)return void console.error(Z);throw new Error(Z)}}return t}parseInline(e,n){n=n||this.renderer;let i,s,l,t="";const a=e.length;for(i=0;i<a;i++)if(s=e[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]&&(l=this.options.extensions.renderers[s.type].call({parser:this},s),!1!==l||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)))t+=l||"";else switch(s.type){case"escape":case"text":t+=n.text(s.text);break;case"html":t+=n.html(s.text);break;case"link":t+=n.link(s.href,s.title,this.parseInline(s.tokens,n));break;case"image":t+=n.image(s.href,s.title,s.text);break;case"strong":t+=n.strong(this.parseInline(s.tokens,n));break;case"em":t+=n.em(this.parseInline(s.tokens,n));break;case"codespan":t+=n.codespan(s.text);break;case"br":t+=n.br();break;case"del":t+=n.del(this.parseInline(s.tokens,n));break;default:{const c='Token with "'+s.type+'" type was not found.';if(this.options.silent)return void console.error(c);throw new Error(c)}}return t}}function u(r,e,n){if(typeof r>"u"||null===r)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof r)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected");if("function"==typeof e&&(n=e,e=null),J(e=y({},u.defaults,e||{})),n){const t=e.highlight;let i;try{i=z.lex(r,e)}catch(a){return n(a)}const s=function(a){let c;if(!a)try{e.walkTokens&&u.walkTokens(i,e.walkTokens),c=_.parse(i,e)}catch(g){a=g}return e.highlight=t,a?n(a):n(null,c)};if(!t||t.length<3||(delete e.highlight,!i.length))return s();let l=0;return u.walkTokens(i,function(a){"code"===a.type&&(l++,setTimeout(()=>{t(a.text,a.lang,function(c,g){if(c)return s(c);null!=g&&g!==a.text&&(a.text=g,a.escaped=!0),l--,0===l&&s()})},0))}),void(0===l&&s())}try{const t=z.lex(r,e);return e.walkTokens&&u.walkTokens(t,e.walkTokens),_.parse(t,e)}catch(t){if(t.message+="\nPlease report this to https://github.com/markedjs/marked.",e.silent)return"<p>An error occurred:</p><pre>"+x(t.message+"",!0)+"</pre>";throw t}}u.options=u.setOptions=function(r){return y(u.defaults,r),function te(r){D=r}(u.defaults),u},u.getDefaults=function q(){return{baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}},u.defaults=D,u.use=function(...r){const e=y({},...r),n=u.defaults.extensions||{renderers:{},childTokens:{}};let t;r.forEach(i=>{if(i.extensions&&(t=!0,i.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if(s.renderer){const l=n.renderers?n.renderers[s.name]:null;n.renderers[s.name]=l?function(...a){let c=s.renderer.apply(this,a);return!1===c&&(c=l.apply(this,a)),c}:s.renderer}if(s.tokenizer){if(!s.level||"block"!==s.level&&"inline"!==s.level)throw new Error("extension level must be 'block' or 'inline'");n[s.level]?n[s.level].unshift(s.tokenizer):n[s.level]=[s.tokenizer],s.start&&("block"===s.level?n.startBlock?n.startBlock.push(s.start):n.startBlock=[s.start]:"inline"===s.level&&(n.startInline?n.startInline.push(s.start):n.startInline=[s.start]))}s.childTokens&&(n.childTokens[s.name]=s.childTokens)})),i.renderer){const s=u.defaults.renderer||new U;for(const l in i.renderer){const a=s[l];s[l]=(...c)=>{let g=i.renderer[l].apply(s,c);return!1===g&&(g=a.apply(s,c)),g}}e.renderer=s}if(i.tokenizer){const s=u.defaults.tokenizer||new P;for(const l in i.tokenizer){const a=s[l];s[l]=(...c)=>{let g=i.tokenizer[l].apply(s,c);return!1===g&&(g=a.apply(s,c)),g}}e.tokenizer=s}if(i.walkTokens){const s=u.defaults.walkTokens;e.walkTokens=function(l){i.walkTokens.call(this,l),s&&s.call(this,l)}}t&&(e.extensions=n),u.setOptions(e)})},u.walkTokens=function(r,e){for(const n of r)switch(e.call(u,n),n.type){case"table":for(const t of n.header)u.walkTokens(t.tokens,e);for(const t of n.rows)for(const i of t)u.walkTokens(i.tokens,e);break;case"list":u.walkTokens(n.items,e);break;default:u.defaults.extensions&&u.defaults.extensions.childTokens&&u.defaults.extensions.childTokens[n.type]?u.defaults.extensions.childTokens[n.type].forEach(function(t){u.walkTokens(n[t],e)}):n.tokens&&u.walkTokens(n.tokens,e)}},u.parseInline=function(r,e){if(typeof r>"u"||null===r)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof r)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected");J(e=y({},u.defaults,e||{}));try{const n=z.lexInline(r,e);return e.walkTokens&&u.walkTokens(n,e.walkTokens),_.parseInline(n,e)}catch(n){if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e.silent)return"<p>An error occurred:</p><pre>"+x(n.message+"",!0)+"</pre>";throw n}},u.Parser=_,u.parser=_.parse,u.Renderer=U,u.TextRenderer=V,u.Lexer=z,u.lexer=z.lex,u.Tokenizer=P,u.Slugger=Y,u.parse=u;var xe=R(4101),we=R(3984),be=R(3073),I=R(485);let ye=(()=>{class r{constructor(n){this.injector=n,this.destory$=new xe.x}get sdk(){return this.injector.get(be.v)}ngOnDestroy(){this.destory$.next(),this.destory$.complete()}ngOnInit(){this.getDocs()}get dom(){return this.injector.get(ee.H7)}getDocs(){this.sdk.getStatic("@nger/weibo/assets/docs/readme.md",{}).pipe((0,we.R)(this.destory$)).subscribe({next:n=>{const t=u(n,{gfm:!0});this.html=this.dom.bypassSecurityTrustHtml(t)}})}}return r.\u0275fac=function(n){return new(n||r)(I.Y36(I.zs3))},r.\u0275cmp=I.Xpm({type:r,selectors:[["doc-page"]],decls:1,vars:1,consts:[[3,"innerHTML"]],template:function(n,t){1&n&&I._UZ(0,"div",0),2&n&&I.Q6J("innerHTML",t.html,I.oJD)},styles:["[_nghost-%COMP%]{display:block;max-height:100%;overflow-y:auto;padding:10px 20px}"]}),r})(),$e=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=I.oAB({type:r}),r.\u0275inj=I.cJS({imports:[K.Bz.forChild([{path:"",component:ye}])]}),r})()}}]);