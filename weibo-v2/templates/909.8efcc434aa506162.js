"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[909],{6909:(D,u,r)=>{r.r(u),r.d(u,{NlpModule:()=>Z});var c=r(3233),p=r(5708),d=r(1305),C=r(9575),h=r(2356),t=r(485),m=r(7681),v=r(4284),g=r(5775),f=r(8035);let w=(()=>{class n{constructor(e){this.ele=e,this._val=[],this.title="pie"}set data(e){this._val=e,this.chart&&this.chart.setOption(this.option)}get serie(){return{name:this.title,type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"40",fontWeight:"bold"}},labelLine:{show:!1},data:this._val}}get option(){return{tooltip:{trigger:"item"},backgroundColor:"#fff",legend:{top:"5%",left:"center"},series:[this.serie]}}ngOnDestroy(){}ngOnInit(){this.chart=f.S1(this.ele.nativeElement),this.option&&this.chart.setOption(this.option)}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(t.SBq))},n.\u0275dir=t.lG2({type:n,selectors:[["","echart-pie",""]],inputs:{title:"title",data:"data"}}),n})();var O=r(7018);let x=(()=>{class n{constructor(e){this.ele=e,this._val=[],this.title="pie"}set data(e){e&&e.length>0&&(this._val=e,this.chart&&this.chart.setOption(this.option))}get serie(){return{name:"Pressure",type:"gauge",detail:{formatter:e=>e>72?"\u6b63\u5411":e<40?"\u8d1f\u5411":"\u4e2d\u7acb"},data:this._val}}get option(){return{backgroundColor:"#fff",series:[this.serie]}}ngOnDestroy(){}ngOnInit(){this.chart=f.S1(this.ele.nativeElement),console.log(this.option),this.option&&this.chart.setOption(this.option)}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(t.SBq))},n.\u0275dir=t.lG2({type:n,selectors:[["","echart-gauge",""]],inputs:{title:"title",data:"data"}}),n})();function M(n,s){if(1&n&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&n){const e=s.$implicit;t.xp6(1),t.Oqu(e[0])}}function y(n,s){if(1&n&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&n){const e=s.$implicit;t.Gre("word word-",e.flag,""),t.xp6(1),t.hij(" ",e.word," ")}}let P=(()=>{class n{constructor(e,i,a){this.python=e,this.dom=i,this.injector=a,this.text="\u5927\u90e8\u5206\u521d\u5b66\u8005\u4e00\u5b9a\u4f1a\u8d70\u975e\u5e38\u591a\u7684\u5f2f\u8def\uff0c\u7531\u4e8e\u5bf9\u4e8eIT\u73af\u5883\u7684\u4e0d\u4e86\u89e3\uff0c\u5bf9\u4e8e\u4e13\u4e1a\u6280\u672f\u77e5\u8bc6\u7684\u532e\u4e4f\uff0c\u4f1a\u5bfc\u81f4\u521d\u5b66\u8005\u4e0d\u77e5\u9053\u5b66\u4ec0\u4e48\u5185\u5bb9\uff0c\u4ee5\u53ca\u5e94\u8be5\u5b66\u5230\u4ec0\u4e48\u7a0b\u5ea6\uff0c\u5c31\u4f1a\u5bfc\u81f4\u8d70\u5f88\u591a\u5f2f\u8def\u3002\u5728\u5b66\u4e60\u8fc7\u7a0b\u4e2d\u9047\u5230\u4e00\u4e2a\u95ee\u9898\u597d\u51e0\u4e2a\u5c0f\u65f6\u65e0\u6cd5\u89e3\u51b3\uff0c\u5c31\u4f1a\u5bfc\u81f4\u5b66\u4e60\u6548\u7387\u5f88\u4f4e\uff0c\u6240\u4ee5\u6211\u5efa\u8bae\u5404\u4f4d\u4e00\u5b9a\u8981\u61c2\u5f97\u201c\u501f\u52bf\u201d\uff0c\u60f3\u8981\u8fdb\u5165\u4efb\u4f55\u4e00\u4e2a\u884c\u4e1a\uff0c\u6700\u597d\u662f\u6709\u4e00\u4e2a\u6709\u7ecf\u9a8c\u7684\u4eba\u5e26\uff0c\u4f46\u5982\u679c\u8eab\u8fb9\u6ca1\u6709\u670b\u53cb\u662f\u505a\u8fd9\u65b9\u9762\u7684\uff0c\u5c31\u8981\u501f\u52a9\u201c\u7f51\u53cb\u201d\u7684\u529b\u91cf\uff0c\u6211\u6bd5\u7adf\u5e72\u4e866\u5e74\u7684\u524d\u7aef\u5f00\u53d1\uff0c\u4e5f\u6709\u975e\u5e38\u591a\u7684\u8d44\u6e90\uff0c\u6211\u5c31\u5efa\u7acb\u4e86\u4e00\u4e2a\u4e13\u95e8\u4ea4\u6d41\u524d\u7aef\u65b9\u9762\u95ee\u9898\u7684\u5b66\u4e60\u7fa4\uff0c\u91cc\u9762\u4e5f\u6709\u5f88\u591a\u5927\u516c\u53f8\u7684\u6280\u672f\u5927\u725b\u3002\u5f88\u591a\u65f6\u5019\uff0c\u6280\u672f\u5927\u725b\u7684\u51e0\u53e5\u8bdd\u5c31\u4f1a\u8ba9\u6211\u4eec\u918d\u9190\u704c\u9876\uff0c\u5c11\u6d6a\u8d39\u65f6\u95f4\uff0c\u5982\u679c\u60f3\u8981\u591a\u8ddf\u6709\u7ecf\u9a8c\u7684\u4eba\u5b66\u4e60\uff0c\u5c31\u70b9\u51fb\u4e0b\u9762\u52a0\u5165\u6211\u7684\u524d\u7aef\u4ea4\u6d41\u7fa4\uff0c\u4ee5\u540e\u6709\u5de5\u4f5c\u7684\u5185\u63a8\u673a\u4f1a\u90fd\u76f8\u4e92\u63a8\u8350\u4e00\u4e0b\uff0c\u6bd5\u7adf\u6211\u4eec\u662f\u5173\u7cfb\u793e\u4f1a\u3002",this.preview=[],this.count=[],this.keywords=[],this.sentiments=[],this.word_cloud=[],this.cover="assets/001.png",this.stop_words=["\u6709","\u4f1a","\u662f"],this.readability=0}change(){this.python.post("/v1/cut",{text:this.text}).subscribe(e=>{this.preview=e.data;const i=e.count;this.count=Object.keys(i).map(o=>({name:o,value:i[o]})).sort((o,l)=>l.value-o.value),this.keywords=e.keywords.map(([o,l])=>[o,`${Math.floor(100*l)}px`]),this.sentiments=[{value:100*e.sentiments,name:"\u60c5\u611f\u5f3a\u5ea6"}];const a={};this.preview.map(o=>{if((o.flag.startsWith("n")||o.flag.startsWith("v"))&&!this.stop_words.includes(o.word)){const l=Reflect.get(a,o.word)||0;Reflect.set(a,o.word,l+1)}}),this.word_cloud=Object.keys(a).map(o=>({name:o,value:Reflect.get(a,o)})),this.readability=e.readability})}ngOnDestroy(){}get http(){return this.injector.get(h.eN)}ngOnInit(){const e=new h.WM;e.set("Content-Type","application/x-www-form-urlencoded"),this.http.post("http://localhost:8082/cut",{texts:["\u4f60\u597d"]},{headers:e}).subscribe(i=>console.log(i)),this.change()}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(m.i),t.Y36(v.H7),t.Y36(t.zs3))},n.\u0275cmp=t.Xpm({type:n,selectors:[["nlp-cut"]],decls:16,vars:7,consts:[[1,"cut-form"],["rows","5","nz-input","","placeholder","\u8bf7\u8f93\u5165\u8981\u5206\u8bcd\u7684\u6587\u672c",3,"ngModel","ngModelChange"],[1,"title"],[1,"keyword-box"],[4,"ngFor","ngForOf"],[1,"cut-preview"],[3,"class",4,"ngFor","ngForOf"],[1,"cut-count"],["echart-pie","","title","\u5206\u8bcd\u7edf\u8ba1",2,"height","450px","flex","1",3,"data"],["word-cloud","",1,"keyword-box",2,"height","450px","flex","1",3,"data"],["echart-gauge","","title","\u60c5\u611f\u5f3a\u5ea6",2,"height","450px","flex","1",3,"data"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0)(1,"textarea",1),t.NdJ("ngModelChange",function(o){return i.text=o})("ngModelChange",function(){return i.change()}),t.qZA()(),t.TgZ(2,"h5",2),t._uU(3,"\u5173\u952e\u5b57\u63d0\u53d6"),t.qZA(),t.TgZ(4,"div",3),t.YNc(5,M,2,1,"span",4),t.qZA(),t.TgZ(6,"h5",2),t._uU(7),t.qZA(),t.TgZ(8,"div",5),t.YNc(9,y,2,4,"span",6),t.qZA(),t.TgZ(10,"h5",2),t._uU(11,"\u5206\u8bcd\u7edf\u8ba1"),t.qZA(),t.TgZ(12,"div",7),t._UZ(13,"div",8)(14,"div",9)(15,"div",10),t.qZA()),2&e&&(t.xp6(1),t.Q6J("ngModel",i.text),t.xp6(4),t.Q6J("ngForOf",i.keywords),t.xp6(2),t.hij("\u5206\u8bcd\u7ed3\u679c(\u53ef\u8bfb\u6027",i.readability,")"),t.xp6(2),t.Q6J("ngForOf",i.preview),t.xp6(4),t.Q6J("data",i.count),t.xp6(1),t.Q6J("data",i.word_cloud),t.xp6(1),t.Q6J("data",i.sentiments))},dependencies:[d.Zp,c.Fj,c.JJ,c.On,g.sg,w,O.A,x],styles:["[_nghost-%COMP%]{padding:10px 20px;display:block;overflow-y:auto;max-height:100%;max-width:100%}.cut-form[_ngcontent-%COMP%]{display:block;margin-top:20px}.cut-form[_ngcontent-%COMP%]   .cut-all[_ngcontent-%COMP%]{margin-left:10px;margin-bottom:10px}.keyword-box[_ngcontent-%COMP%]{background:#fff;padding:5px 10px}.keyword-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{margin-left:5px}.cut-count[_ngcontent-%COMP%]{display:flex;max-width:100%;width:100%;overflow:hidden}.cut-count[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{flex:1;overflow:hidden}.cut-preview[_ngcontent-%COMP%]{font-size:14px;background:#fff;padding:5px 10px}.cut-preview[_ngcontent-%COMP%]   .word[_ngcontent-%COMP%]{margin-left:5px}.cut-preview[_ngcontent-%COMP%]   .word[_ngcontent-%COMP%]   .word-type[_ngcontent-%COMP%]{font-size:8px;color:#4b4b4b}.title[_ngcontent-%COMP%]{margin:0;font-size:16px;line-height:50px;height:50px;font-weight:600}.word-m[_ngcontent-%COMP%]{color:#0ff}.word-n[_ngcontent-%COMP%]{color:red}.word-d[_ngcontent-%COMP%]{color:#00f}.word-v[_ngcontent-%COMP%]{color:#adff2f}.word-c[_ngcontent-%COMP%]{color:#f0f}.word-p[_ngcontent-%COMP%]{color:#deb887}.word-r[_ngcontent-%COMP%]{color:teal}.word-a[_ngcontent-%COMP%]{color:#8b4513}.word-f[_ngcontent-%COMP%]{color:gray}"]}),n})();var b=r(4426),F=r(1117);let Z=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[d.o7,c.u5,C.LV,b.aF,g.ez,F.h,p.Bz.forChild([{path:"",pathMatch:"full",redirectTo:"nlp-cut"},{path:"nlp-cut",component:P}]),p.Bz]}),n})()}}]);