@import "base.scss";
.nav {
    position: fixed;
    right: 0;
    z-index: 20000;
    width: 30%;
    
    height: 10%;
    font-size: px2em(28, 64);
    ul {
        list-style: none;
        min-width: 440px;
        width: 100%;
    }
    ul a {
        display: block;
        text-decoration: none;
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 4.5;
        color: white;
        /* background-color: #2f3e45; */
    }
}

.drop-down {
    width: 100%;
    height: 100%;
}

.drop-down-content li {
    display: inline-block;
    float: left;
    width: 20%;
    min-width: 82px;
    height: 100%;
}

.drop-down {
    /*position: relative;*/
    width: 100%;
}

.drop-down-content {
    padding: 0;
    display: none;
    position: absolute;
    top: 37%;
    right: 0;
    /*position: absolute;*/
}

h3 {
    font-size: .5625em;
    clear: both;
}

.drop-down-content li:hover a {
    color: bisque
}

.nav .drop-down:hover .drop-down-content {
    display: block;
}