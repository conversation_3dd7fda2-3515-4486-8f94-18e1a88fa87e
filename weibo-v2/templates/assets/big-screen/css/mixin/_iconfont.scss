@mixin iconfont($font, $http: http) {
    @font-face {
        font-family: "iconfont";
        /* project id 343429 */
        src: url($http + "://at.alicdn.com/t/" + $font + ".eot");
        src: url($http + "://at.alicdn.com/t/" + $font + ".eot?#iefix") format("embedded-opentype"), url($http + "://at.alicdn.com/t/" + $font + ".woff") format("woff"), url($http + "://at.alicdn.com/t/" + $font + ".ttf") format("truetype"), url($http + "://at.alicdn.com/t/" + $font + ".svg#iconfont") format("svg");
    }
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;
}

@include iconfont("font_rl8clcpkugfd2t9")