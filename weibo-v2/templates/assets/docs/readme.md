
## 1. 登录
> http://192.168.2.110:8081/loginAdmin

输入用户名和密码后点击登录按钮！

![登录](assets/docs/images/login.png)

登录成功后，跳转到首页

![首页](assets/docs/images/home.png)

## 2. 基础数据

### 2.1 城市管理
对城市及其代码进行增删改查操作
![城市管理](assets/docs/images/city.png)

### 2.2 行业类型
对行业类型进行增删改查操作
![行业类型](assets/docs/images/event_category.png)
### 2.3 事件性质
对事件性质进行增删改查操作
![事件性质](assets/docs/images/event_type.png)
## 3. 基础操作

### 3.1 添加事件
1. 事件名称（必填）
2. 地域（必选）
3. 事件性质（必选）
4. 行业类型（必选）
5. 备注（必填）
6. 回应帖子（选填）
7. 是否出台政策（选填）
8. 处置措施（选填）
9. 关键字（建议填写，不同关键字用逗号隔开）
![添加事件](assets/docs/images/event.png)

### 3.2 爬取事件
1. 选择一个事件
2. 输入关键字
3. 点击搜索按钮
![爬取事件](assets/docs/images/search.png)

### 3.3 定时爬取事件
1. 选择一个事件
2. 设定运行事件（秒 分 时 日 月 周）
    > 如：每天23点定时运行（* * 23 * * *）
    > 如：每天周一定时运行（* * * * * 1）
    > 如：每月1号定时运行（* * * 1 * *）
3. 点击按钮提交
![定时爬取事件](assets/docs/images/schedule.png)

4. 定时爬取事件关闭

点击<b>删除任务按钮</b>即可
![删除定时爬取事件](assets/docs/images/delete_task.png)


## 4 数据搜索及导出


### 4.1 数据检索


输入搜索条件进行检索
![事件检索](assets/docs/images/search-eventpng.png)


点击详情查看该事件包含的信息

1. 事件话题
![事件主题](assets/docs/images/event_topic.png)
2. 事件帖子
![事件文章](assets/docs/images/event_article.png)
3. 事件评论
![事件评论](assets/docs/images/event_comment.ts)
4. 话题详情
![话题详情](assets/docs/images/topic_detail.png)
6. 帖子详情
![帖子详情](assets/docs/images/article_detail.png)


### 4.2 导出数据
管理员登录后可以看导出功能
![导出数据](assets/docs/images/export.png)


### 4.2 用户管理

![用户管理](assets/docs/images/user_manage.png)

### 4.3 用户导出日志

![用户导出日志](assets/docs/images/export_log.png)
### 4.4 系统更新
![系统更新](assets/docs/images/update.png)

## 5 截面数据
> 定时任务每执行一次，记录一次特定指标参数

### 5.1 话题截面
> 话题记录当前爬取时间的`阅读次数`、`讨论次数`、`原创人数`及`爬取时间`
![话题截面](assets/docs/images/topic_tend.png)
### 5.2 帖子截面
> 帖子截面记录当前爬取时间的`转发数`、`评论数`、`点赞数`及`爬取时间`
![帖子截面](assets/docs/images/article_tend.png)

### 5.3 评论截面
> 评论截面记录当前爬取时间的`点赞数`及`爬取时间`
![评论截面](assets/docs/images/comment_tend.png)

