<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3526456" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe742;</span>
                <div class="name">数据大屏</div>
                <div class="code-name">&amp;#xe742;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">任务</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6eb;</span>
                <div class="name">敏感关键字</div>
                <div class="code-name">&amp;#xe6eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">nlp 自然语言处理</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">无数据</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe690;</span>
                <div class="name">定时</div>
                <div class="code-name">&amp;#xe690;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e9;</span>
                <div class="name">Loading</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b9;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe8b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">添加应用</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">删除2</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb67;</span>
                <div class="name">应用管理</div>
                <div class="code-name">&amp;#xeb67;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb70;</span>
                <div class="name">24gl-move</div>
                <div class="code-name">&amp;#xeb70;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">平移</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">支付宝支付</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe89a;</span>
                <div class="name">24组织架构、接口</div>
                <div class="code-name">&amp;#xe89a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e8;</span>
                <div class="name">符号-权限</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a4;</span>
                <div class="name">综合</div>
                <div class="code-name">&amp;#xe6a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">帮助</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f4;</span>
                <div class="name">bimgis_截面分析</div>
                <div class="code-name">&amp;#xe6f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">类型</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">机构性质</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">行业类型</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">可视化图标0.7-11</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70e;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe70e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87d;</span>
                <div class="name">蜘蛛</div>
                <div class="code-name">&amp;#xe87d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">其它</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">继续</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec61;</span>
                <div class="name">运行</div>
                <div class="code-name">&amp;#xec61;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe800;</span>
                <div class="name">暂停</div>
                <div class="code-name">&amp;#xe800;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">信息爬取</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea45;</span>
                <div class="name">爬取任务管理</div>
                <div class="code-name">&amp;#xea45;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">一键购买</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">登录</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">注销</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">腾讯微博</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">网易</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">social-tieba</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">企鹅</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe744;</span>
                <div class="name">新闻</div>
                <div class="code-name">&amp;#xe744;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">论坛资讯</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">微信</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">抖音</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">更新</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8cb;</span>
                <div class="name">百度</div>
                <div class="code-name">&amp;#xe8cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">账户</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67c;</span>
                <div class="name">登录</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">帖子</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe891;</span>
                <div class="name">评论</div>
                <div class="code-name">&amp;#xe891;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">事件管理</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">城市</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">多媒体</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b0;</span>
                <div class="name">话题符号</div>
                <div class="code-name">&amp;#xe8b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">新浪微博</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">知乎</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">头条样式</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1664095043323') format('woff2'),
       url('iconfont.woff?t=1664095043323') format('woff'),
       url('iconfont.ttf?t=1664095043323') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-shujudaping"></span>
            <div class="name">
              数据大屏
            </div>
            <div class="code-name">.icon-shujudaping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-renwu"></span>
            <div class="name">
              任务
            </div>
            <div class="code-name">.icon-renwu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-minganguanjianzi"></span>
            <div class="name">
              敏感关键字
            </div>
            <div class="code-name">.icon-minganguanjianzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nlpziranyuyanchuli"></span>
            <div class="name">
              nlp 自然语言处理
            </div>
            <div class="code-name">.icon-nlpziranyuyanchuli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wushuju"></span>
            <div class="name">
              无数据
            </div>
            <div class="code-name">.icon-wushuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingshi"></span>
            <div class="name">
              定时
            </div>
            <div class="code-name">.icon-dingshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Loading"></span>
            <div class="name">
              Loading
            </div>
            <div class="code-name">.icon-Loading
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin1"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiayingyong"></span>
            <div class="name">
              添加应用
            </div>
            <div class="code-name">.icon-tianjiayingyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu2"></span>
            <div class="name">
              删除2
            </div>
            <div class="code-name">.icon-shanchu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu1"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingyongguanli"></span>
            <div class="name">
              应用管理
            </div>
            <div class="code-name">.icon-yingyongguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-24gl-move"></span>
            <div class="name">
              24gl-move
            </div>
            <div class="code-name">.icon-24gl-move
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-move"></span>
            <div class="name">
              平移
            </div>
            <div class="code-name">.icon-move
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhifubaozhifu"></span>
            <div class="name">
              支付宝支付
            </div>
            <div class="code-name">.icon-zhifubaozhifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuzhijiagoujiekou"></span>
            <div class="name">
              24组织架构、接口
            </div>
            <div class="code-name">.icon-zuzhijiagoujiekou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanxian"></span>
            <div class="name">
              符号-权限
            </div>
            <div class="code-name">.icon-quanxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziyuan"></span>
            <div class="name">
              综合
            </div>
            <div class="code-name">.icon-ziyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bangzhu"></span>
            <div class="name">
              帮助
            </div>
            <div class="code-name">.icon-bangzhu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bimgis_jiemianfenxi"></span>
            <div class="name">
              bimgis_截面分析
            </div>
            <div class="code-name">.icon-bimgis_jiemianfenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-leixing"></span>
            <div class="name">
              类型
            </div>
            <div class="code-name">.icon-leixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiaozhizuomoban_jigouxingzhi"></span>
            <div class="name">
              机构性质
            </div>
            <div class="code-name">.icon-tubiaozhizuomoban_jigouxingzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hangyeleixing"></span>
            <div class="name">
              行业类型
            </div>
            <div class="code-name">.icon-hangyeleixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-keshihuatubiao-"></span>
            <div class="name">
              可视化图标0.7-11
            </div>
            <div class="code-name">.icon-keshihuatubiao-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhizhu"></span>
            <div class="name">
              蜘蛛
            </div>
            <div class="code-name">.icon-zhizhu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qita"></span>
            <div class="name">
              其它
            </div>
            <div class="code-name">.icon-qita
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jixu"></span>
            <div class="name">
              继续
            </div>
            <div class="code-name">.icon-jixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunhang"></span>
            <div class="name">
              运行
            </div>
            <div class="code-name">.icon-yunhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zanting"></span>
            <div class="name">
              暂停
            </div>
            <div class="code-name">.icon-zanting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Information-crawling"></span>
            <div class="name">
              信息爬取
            </div>
            <div class="code-name">.icon-Information-crawling
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paqurenwuguanli"></span>
            <div class="name">
              爬取任务管理
            </div>
            <div class="code-name">.icon-paqurenwuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yijiangoumai"></span>
            <div class="name">
              一键购买
            </div>
            <div class="code-name">.icon-yijiangoumai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-denglu1"></span>
            <div class="name">
              登录
            </div>
            <div class="code-name">.icon-denglu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjia"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icon-tianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuxiao"></span>
            <div class="name">
              注销
            </div>
            <div class="code-name">.icon-zhuxiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tengxunweibo"></span>
            <div class="name">
              腾讯微博
            </div>
            <div class="code-name">.icon-tengxunweibo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wangyi"></span>
            <div class="name">
              网易
            </div>
            <div class="code-name">.icon-wangyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-social-tieba"></span>
            <div class="name">
              social-tieba
            </div>
            <div class="code-name">.icon-social-tieba
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-034"></span>
            <div class="name">
              企鹅
            </div>
            <div class="code-name">.icon-034
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinwen"></span>
            <div class="name">
              新闻
            </div>
            <div class="code-name">.icon-xinwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-luntanzixun"></span>
            <div class="name">
              论坛资讯
            </div>
            <div class="code-name">.icon-luntanzixun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixin"></span>
            <div class="name">
              微信
            </div>
            <div class="code-name">.icon-weixin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-douyin"></span>
            <div class="name">
              抖音
            </div>
            <div class="code-name">.icon-douyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengxin"></span>
            <div class="name">
              更新
            </div>
            <div class="code-name">.icon-gengxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baidu"></span>
            <div class="name">
              百度
            </div>
            <div class="code-name">.icon-baidu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhanghu"></span>
            <div class="name">
              账户
            </div>
            <div class="code-name">.icon-zhanghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-denglu"></span>
            <div class="name">
              登录
            </div>
            <div class="code-name">.icon-denglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiezi"></span>
            <div class="name">
              帖子
            </div>
            <div class="code-name">.icon-tiezi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pinglun"></span>
            <div class="name">
              评论
            </div>
            <div class="code-name">.icon-pinglun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shijianguanli"></span>
            <div class="name">
              事件管理
            </div>
            <div class="code-name">.icon-shijianguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chengshi"></span>
            <div class="name">
              城市
            </div>
            <div class="code-name">.icon-chengshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duomeiti"></span>
            <div class="name">
              多媒体
            </div>
            <div class="code-name">.icon-duomeiti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huatifuhao"></span>
            <div class="name">
              话题符号
            </div>
            <div class="code-name">.icon-huatifuhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinlangweibo"></span>
            <div class="name">
              新浪微博
            </div>
            <div class="code-name">.icon-xinlangweibo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shejiaotubiao-46"></span>
            <div class="name">
              知乎
            </div>
            <div class="code-name">.icon-shejiaotubiao-46
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-toutiaoyangshi"></span>
            <div class="name">
              头条样式
            </div>
            <div class="code-name">.icon-toutiaoyangshi
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujudaping"></use>
                </svg>
                <div class="name">数据大屏</div>
                <div class="code-name">#icon-shujudaping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renwu"></use>
                </svg>
                <div class="name">任务</div>
                <div class="code-name">#icon-renwu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-minganguanjianzi"></use>
                </svg>
                <div class="name">敏感关键字</div>
                <div class="code-name">#icon-minganguanjianzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nlpziranyuyanchuli"></use>
                </svg>
                <div class="name">nlp 自然语言处理</div>
                <div class="code-name">#icon-nlpziranyuyanchuli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wushuju"></use>
                </svg>
                <div class="name">无数据</div>
                <div class="code-name">#icon-wushuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingshi"></use>
                </svg>
                <div class="name">定时</div>
                <div class="code-name">#icon-dingshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Loading"></use>
                </svg>
                <div class="name">Loading</div>
                <div class="code-name">#icon-Loading</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin1"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiayingyong"></use>
                </svg>
                <div class="name">添加应用</div>
                <div class="code-name">#icon-tianjiayingyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu2"></use>
                </svg>
                <div class="name">删除2</div>
                <div class="code-name">#icon-shanchu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu1"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingyongguanli"></use>
                </svg>
                <div class="name">应用管理</div>
                <div class="code-name">#icon-yingyongguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-24gl-move"></use>
                </svg>
                <div class="name">24gl-move</div>
                <div class="code-name">#icon-24gl-move</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-move"></use>
                </svg>
                <div class="name">平移</div>
                <div class="code-name">#icon-move</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhifubaozhifu"></use>
                </svg>
                <div class="name">支付宝支付</div>
                <div class="code-name">#icon-zhifubaozhifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuzhijiagoujiekou"></use>
                </svg>
                <div class="name">24组织架构、接口</div>
                <div class="code-name">#icon-zuzhijiagoujiekou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanxian"></use>
                </svg>
                <div class="name">符号-权限</div>
                <div class="code-name">#icon-quanxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuan"></use>
                </svg>
                <div class="name">综合</div>
                <div class="code-name">#icon-ziyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bangzhu"></use>
                </svg>
                <div class="name">帮助</div>
                <div class="code-name">#icon-bangzhu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bimgis_jiemianfenxi"></use>
                </svg>
                <div class="name">bimgis_截面分析</div>
                <div class="code-name">#icon-bimgis_jiemianfenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-leixing"></use>
                </svg>
                <div class="name">类型</div>
                <div class="code-name">#icon-leixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiaozhizuomoban_jigouxingzhi"></use>
                </svg>
                <div class="name">机构性质</div>
                <div class="code-name">#icon-tubiaozhizuomoban_jigouxingzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hangyeleixing"></use>
                </svg>
                <div class="name">行业类型</div>
                <div class="code-name">#icon-hangyeleixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-keshihuatubiao-"></use>
                </svg>
                <div class="name">可视化图标0.7-11</div>
                <div class="code-name">#icon-keshihuatubiao-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhizhu"></use>
                </svg>
                <div class="name">蜘蛛</div>
                <div class="code-name">#icon-zhizhu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qita"></use>
                </svg>
                <div class="name">其它</div>
                <div class="code-name">#icon-qita</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jixu"></use>
                </svg>
                <div class="name">继续</div>
                <div class="code-name">#icon-jixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunhang"></use>
                </svg>
                <div class="name">运行</div>
                <div class="code-name">#icon-yunhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zanting"></use>
                </svg>
                <div class="name">暂停</div>
                <div class="code-name">#icon-zanting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Information-crawling"></use>
                </svg>
                <div class="name">信息爬取</div>
                <div class="code-name">#icon-Information-crawling</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paqurenwuguanli"></use>
                </svg>
                <div class="name">爬取任务管理</div>
                <div class="code-name">#icon-paqurenwuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yijiangoumai"></use>
                </svg>
                <div class="name">一键购买</div>
                <div class="code-name">#icon-yijiangoumai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-denglu1"></use>
                </svg>
                <div class="name">登录</div>
                <div class="code-name">#icon-denglu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjia"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icon-tianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuxiao"></use>
                </svg>
                <div class="name">注销</div>
                <div class="code-name">#icon-zhuxiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tengxunweibo"></use>
                </svg>
                <div class="name">腾讯微博</div>
                <div class="code-name">#icon-tengxunweibo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangyi"></use>
                </svg>
                <div class="name">网易</div>
                <div class="code-name">#icon-wangyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-social-tieba"></use>
                </svg>
                <div class="name">social-tieba</div>
                <div class="code-name">#icon-social-tieba</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-034"></use>
                </svg>
                <div class="name">企鹅</div>
                <div class="code-name">#icon-034</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinwen"></use>
                </svg>
                <div class="name">新闻</div>
                <div class="code-name">#icon-xinwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-luntanzixun"></use>
                </svg>
                <div class="name">论坛资讯</div>
                <div class="code-name">#icon-luntanzixun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixin"></use>
                </svg>
                <div class="name">微信</div>
                <div class="code-name">#icon-weixin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-douyin"></use>
                </svg>
                <div class="name">抖音</div>
                <div class="code-name">#icon-douyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengxin"></use>
                </svg>
                <div class="name">更新</div>
                <div class="code-name">#icon-gengxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baidu"></use>
                </svg>
                <div class="name">百度</div>
                <div class="code-name">#icon-baidu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhanghu"></use>
                </svg>
                <div class="name">账户</div>
                <div class="code-name">#icon-zhanghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-denglu"></use>
                </svg>
                <div class="name">登录</div>
                <div class="code-name">#icon-denglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiezi"></use>
                </svg>
                <div class="name">帖子</div>
                <div class="code-name">#icon-tiezi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pinglun"></use>
                </svg>
                <div class="name">评论</div>
                <div class="code-name">#icon-pinglun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijianguanli"></use>
                </svg>
                <div class="name">事件管理</div>
                <div class="code-name">#icon-shijianguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chengshi"></use>
                </svg>
                <div class="name">城市</div>
                <div class="code-name">#icon-chengshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duomeiti"></use>
                </svg>
                <div class="name">多媒体</div>
                <div class="code-name">#icon-duomeiti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huatifuhao"></use>
                </svg>
                <div class="name">话题符号</div>
                <div class="code-name">#icon-huatifuhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinlangweibo"></use>
                </svg>
                <div class="name">新浪微博</div>
                <div class="code-name">#icon-xinlangweibo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shejiaotubiao-46"></use>
                </svg>
                <div class="name">知乎</div>
                <div class="code-name">#icon-shejiaotubiao-46</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-toutiaoyangshi"></use>
                </svg>
                <div class="name">头条样式</div>
                <div class="code-name">#icon-toutiaoyangshi</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
